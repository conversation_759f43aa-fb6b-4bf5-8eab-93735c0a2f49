<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{3640F504-8B8C-4836-A6D1-6C10861DFFE6}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>JieNor.AMS.YDJ.Store.AppService</RootNamespace>
    <AssemblyName>JieNor.AMS.YDJ.Store.AppService</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\lib\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <UseVSHostingProcess>true</UseVSHostingProcess>
    <NoWarn>CS1591;CS1573;CS1587</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <RunPostBuildEvent>Always</RunPostBuildEvent>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AlibabaCloud.EndpointUtil, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AlibabaCloud.EndpointUtil.0.1.1\lib\net45\AlibabaCloud.EndpointUtil.dll</HintPath>
    </Reference>
    <Reference Include="AlibabaCloud.GatewaySpi, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AlibabaCloud.GatewaySpi.0.0.1\lib\net45\AlibabaCloud.GatewaySpi.dll</HintPath>
    </Reference>
    <Reference Include="AlibabaCloud.OpenApiClient, Version=0.1.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AlibabaCloud.OpenApiClient.0.1.3\lib\net45\AlibabaCloud.OpenApiClient.dll</HintPath>
    </Reference>
    <Reference Include="AlibabaCloud.OpenApiUtil, Version=1.0.13.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AlibabaCloud.OpenApiUtil.1.0.15\lib\net45\AlibabaCloud.OpenApiUtil.dll</HintPath>
    </Reference>
    <Reference Include="AlibabaCloud.RPCClient, Version=1.0.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AlibabaCloud.RPCClient.1.0.1\lib\net45\AlibabaCloud.RPCClient.dll</HintPath>
    </Reference>
    <Reference Include="AlibabaCloud.SDK.Common, Version=0.2.4.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AlibabaCloud.SDK.Common.0.2.5\lib\net45\AlibabaCloud.SDK.Common.dll</HintPath>
    </Reference>
    <Reference Include="AlibabaCloud.SDK.Dysmsapi20170525, Version=2.0.9.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AlibabaCloud.SDK.Dysmsapi20170525.2.0.10\lib\net45\AlibabaCloud.SDK.Dysmsapi20170525.dll</HintPath>
    </Reference>
    <Reference Include="AlibabaCloud.TeaUtil, Version=0.1.13.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AlibabaCloud.TeaUtil.0.1.13\lib\net45\AlibabaCloud.TeaUtil.dll</HintPath>
    </Reference>
    <Reference Include="AlibabaCloud.TeaXML, Version=0.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AlibabaCloud.TeaXML.0.0.3\lib\net45\AlibabaCloud.TeaXML.dll</HintPath>
    </Reference>
    <Reference Include="Aliyun.Credentials, Version=1.3.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Aliyun.Credentials.1.3.1\lib\net45\Aliyun.Credentials.dll</HintPath>
    </Reference>
    <Reference Include="Autofac, Version=4.6.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.4.6.0\lib\net45\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.AMS.YDJ.Core, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.AMS.YDJ.Core.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.AMS.YDJ.DataTransferObject, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.AMS.YDJ.DataTransferObject.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.AMS.YDJ.WeiXin.AppService">
      <HintPath>..\..\lib\Debug\JieNor.AMS.YDJ.WeiXin.AppService.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\debug\JieNor.Framework.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.AppService, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.AppService.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.BizExpression, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\debug\JieNor.Framework.BizExpression.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.DataEntity, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.DataEntity.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.DataTransferObject, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\debug\JieNor.Framework.DataTransferObject.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.IM, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\debug\JieNor.Framework.IM.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.Interface, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\debug\JieNor.Framework.Interface.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.MetaCore, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.MetaCore.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.SuperOrm, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\debug\JieNor.Framework.SuperOrm.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.SignalR.Core, Version=2.2.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.SignalR.Core.2.2.2\lib\net45\Microsoft.AspNet.SignalR.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=3.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.3.1.0\lib\net45\Microsoft.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=3.1.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.3.1.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="MySql.Data, Version=6.10.7.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="PresentationFramework" />
    <Reference Include="Senparc.Weixin, Version=4.20.2.28750, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\debug\Senparc.Weixin.dll</HintPath>
    </Reference>
    <Reference Include="Senparc.Weixin.Work, Version=1.3.1.28917, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\Senparc.Weixin.Work.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Interfaces, Version=4.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Redis, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Redis.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Common, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\..\..\testbaget\lib\Debug\System.Data.Common.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http.Extensions, Version=2.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.Primitives, Version=4.2.29.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Net.Http.2.2.29\lib\net45\System.Net.Http.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Security" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="Tea, Version=1.0.11.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Tea.1.0.11\lib\net45\Tea.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BaseFormProvider.cs" />
    <Compile Include="Clients\BaseDTO.cs" />
    <Compile Include="Clients\Ewc\AdvancedAPIs\MassApi.cs" />
    <Compile Include="Clients\Ewc\BeiJingTime.cs" />
    <Compile Include="Clients\Ewc\CheckSignature.cs" />
    <Compile Include="Clients\Ewc\Config\ConfigExtentions.cs" />
    <Compile Include="Clients\Ewc\Config\CustomerConfig.cs" />
    <Compile Include="Clients\Ewc\DateTimeUtil.cs" />
    <Compile Include="Clients\Ewc\DTO\EwcBaseDTO.cs" />
    <Compile Include="Clients\Ewc\DTO\EwcGetConsumerWxaCodeDTO.cs" />
    <Compile Include="Clients\Ewc\DTO\EwcSendTextMsgDTO.cs" />
    <Compile Include="Clients\Ewc\DTO\EwcSendMiniProgramMsgDTO.cs" />
    <Compile Include="Clients\Ewc\EwcBaseResponse.cs" />
    <Compile Include="Clients\Ewc\EwcJsonClient.cs" />
    <Compile Include="Clients\Ewc\Model\QyWxUserInfoModel.cs" />
    <Compile Include="DefaultWidgetUserProfile.cs" />
    <Compile Include="Enums\Eum_PieceSend.cs" />
    <Compile Include="Enums\TransferOrderStatus.cs" />
    <Compile Include="Enums\OrderButton.cs" />
    <Compile Include="FinanceBaseService.cs" />
    <Compile Include="GetResultBrandData.cs" />
    <Compile Include="Helper\DirectHelper.cs" />
    <Compile Include="Helper\RSAHelper.cs" />
    <Compile Include="Helper\WriteLogHelper.cs" />
    <Compile Include="Model\BaseDataModel.cs" />
    <Compile Include="Model\DeliveryScanTask\BCReturnDataModel.cs" />
    <Compile Include="Model\DeliveryScanTask\DeliveryTaskListModel.cs" />
    <Compile Include="Model\DeliveryScanTask\ScanData.cs" />
    <Compile Include="Model\Inventoryverify\InventoryTaskListModel.cs" />
    <Compile Include="Model\Inventoryverify\ScanVerificationModel.cs" />
    <Compile Include="Model\PurchaseOrder\PurOrderProductModel.cs" />
    <Compile Include="Model\StockQuery\StockQueryModel.cs" />
    <Compile Include="Model\Inventoryverify\SubmitScanData.cs" />
    <Compile Include="Model\StoreValueModel.cs" />
    <Compile Include="Model\Unboxing\BarCodeInfo.cs" />
    <Compile Include="Model\Unboxing\PrintDataModel.cs" />
    <Compile Include="Model\ZY\SAPSoStockOutModel.cs" />
    <Compile Include="MuSi\Api\MusiAuthService.cs" />
    <Compile Include="MuSi\Api\MuSiMemberApi.cs" />
    <Compile Include="MuSi\Api\MuSiAIApi.cs" />
    <Compile Include="MuSi\Api\MusiMenberCDPApi.cs" />
    <Compile Include="MuSi\Client\MuSiAIClient.cs" />
    <Compile Include="MuSi\DataTransferObject\FromMuSiSyncResponse.cs" />
    <Compile Include="MuSi\DataTransferObject\ToMuSiSetting.cs" />
    <Compile Include="MuSi\DTO\BaseDTO.cs" />
    <Compile Include="MuSi\DTO\CustomerRecordDTO.cs" />
    <Compile Include="MuSi\DTO\HSUndoBillDTO.cs" />
    <Compile Include="MuSi\DTO\MsAuthModifyPwdDto.cs" />
    <Compile Include="MuSi\DTO\MSE3LogisticsDTO.cs" />
    <Compile Include="MuSi\DTO\PushRenewalOrderDTO.cs" />
    <Compile Include="MuSi\DTO\SoStockOutDTO.cs" />
    <Compile Include="MuSi\DTO\SyncAIDTO.cs" />
    <Compile Include="MuSi\DTO\OmsOrderPogressDTO.cs" />
    <Compile Include="MuSi\Enums\Enu_MuSiSyncMode.cs" />
    <Compile Include="MuSi\Enums\Enu_MuSiSyncDir.cs" />
    <Compile Include="MuSi\Enums\Enu_MuSiSyncTimePoint.cs" />
    <Compile Include="MuSi\FormService\SystemIntegration\AfterExecuteEventArgs.cs" />
    <Compile Include="MuSi\FormService\SystemIntegration\AfterSaveEventArgs.cs" />
    <Compile Include="MuSi\FormService\SystemIntegration\EntryFieldMappingEventArgs.cs" />
    <Compile Include="MuSi\FormService\SystemIntegration\BeforeSaveEventArgs.cs" />
    <Compile Include="MuSi\Plugin\STE\Channel\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\FIN\IncomeDisburse\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\NormalOrderSubmitHQ.cs" />
    <Compile Include="MuSi\Plugin\FIN\IncomeDisburse\IncomeSyncHeadquart.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\ReNewOrderSubmitHQ.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\TransferToMuSiShareBillNo.cs" />
    <Compile Include="MuSi\Service\InitAgentBrandData.cs" />
    <Compile Include="MuSi\Interface\SystemIntegration\AbstractSyncDataFromMuSiPlugIn.cs" />
    <Compile Include="MuSi\Interface\SystemIntegration\ISyncDataFromMuSiPlugIn.cs" />
    <Compile Include="MuSi\MetaCore\JsonDynamicDataRow.cs" />
    <Compile Include="MuSi\MuSiSaleApi.cs" />
    <Compile Include="MuSi\MuSiHelper.cs" />
    <Compile Include="MuSi\OperationService\FromMuSi.cs" />
    <Compile Include="MuSi\OperationService\Expression.cs" />
    <Compile Include="MuSi\OperationService\SyncFromMusi.cs" />
    <Compile Include="MuSi\OperationService\SyncToMusiOMS.cs" />
    <Compile Include="MuSi\OperationService\ToMuSi.cs" />
    <Compile Include="MuSi\OperationService\ToMuSiOMS.cs" />
    <Compile Include="MuSi\Plugin\AFT\Vist\MSCancel.cs" />
    <Compile Include="MuSi\Plugin\AFT\Vist\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\AFT\Vist\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\BAS\Brand\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\BAS\Department\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\BAS\Position\AbstractQueryDyn.cs" />
    <Compile Include="MuSi\Plugin\BAS\Position\FuzzyQuery.cs" />
    <Compile Include="MuSi\Plugin\BAS\Position\QuerySelector.cs" />
    <Compile Include="MuSi\Plugin\BAS\Product\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\BAS\RenewType\Delete.cs" />
    <Compile Include="MuSi\Plugin\BAS\RenewType\Save.cs" />
    <Compile Include="MuSi\Plugin\BAS\Staff\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\CommonDraft.cs" />
    <Compile Include="MuSi\Plugin\AIBedCommonSave.cs" />
    <Compile Include="MuSi\Plugin\BAS\Agent\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\BAS\Agent\MainAgentConfigDelete.cs" />
    <Compile Include="MuSi\Plugin\BAS\Agent\MainAgentConfigUnForbid.cs" />
    <Compile Include="MuSi\Plugin\BAS\Agent\MainAgentConfigForbid.cs" />
    <Compile Include="MuSi\Plugin\BAS\Agent\MainAgentConfigSave.cs" />
    <Compile Include="MuSi\Plugin\BAS\BizTask\TaskSave.cs" />
    <Compile Include="MuSi\Plugin\BAS\Deliver\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\BAS\Promotion\PromotCombineFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\BAS\ScanTask\AbstractQueryDyn.cs" />
    <Compile Include="MuSi\Plugin\BAS\ScanTask\QuerySelectorDyn.cs" />
    <Compile Include="MuSi\Plugin\BAS\Series\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\BAS\Store\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\DeleteRow.cs" />
    <Compile Include="MuSi\Plugin\Demo.cs" />
    <Compile Include="MuSi\Plugin\DeleteDataSyncResult.cs" />
    <Compile Include="MuSi\Plugin\FIN\HeadStatement\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\MS\AIBedOrder\Save.cs" />
    <Compile Include="MuSi\Plugin\MS\Boss\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\MS\CrmDistributor\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\MS\CrmDistributor\RefreshCrmDistributor.cs" />
    <Compile Include="MuSi\Plugin\MS\FineBI\getFineBIRSAKey.cs" />
    <Compile Include="MuSi\Plugin\MS\FineBI\GetMenuInfo.cs" />
    <Compile Include="MuSi\Plugin\MS\MarkAssistant\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\MS\MemberActivity\New.cs" />
    <Compile Include="MuSi\Plugin\MS\MemberLogin\CdpLogin.cs" />
    <Compile Include="MuSi\Plugin\MS\MemberLogin\LoadDatas.cs" />
    <Compile Include="MuSi\Plugin\MS\MemberManagementAndAnalysis\New.cs" />
    <Compile Include="MuSi\Plugin\MS\MqSyncRecord\BatchAddMqSync.cs" />
    <Compile Include="MuSi\Plugin\MS\MqSyncRecord\GetMqSyncRecordInfo.cs" />
    <Compile Include="MuSi\Plugin\MS\MqSyncRecord\ToSync.cs" />
    <Compile Include="MuSi\Plugin\MS\SalesChannelOrg\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\MS\StoreSeries\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\MS\DeliverSeries\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\MS\ValueAddedServiceReport\New.cs" />
    <Compile Include="MuSi\Plugin\MS\Value\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrderChg\TransferToMusi.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrderChg\TransferToMuSiOMS.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrder\GetParentProduct.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrder\GetPurchaseOrderChgUtil.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrder\SubmitHQAgain.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrder\ReNewSubmitHQ.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrder\TransferToMuSiOMS.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrder\SubmitHQValidation.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrder\SubmitHQ_Chg.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchasePrice\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\SAL\TransferOrderApply\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\SEC\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\SEL\PropValue\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\SEL\SelFittingsMap\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\Ser\Engineer\MSCancel.cs" />
    <Compile Include="MuSi\Plugin\Ser\Engineer\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\Ser\Service\MSCancel.cs" />
    <Compile Include="MuSi\Plugin\Ser\Service\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\Ser\Service\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\SI\MuSiBizObjMap\QueryDta.cs" />
    <Compile Include="MuSi\Plugin\SI\OperationLog\Retry.cs" />
    <Compile Include="MuSi\Plugin\STE\AfterFeedback\MSCancel.cs" />
    <Compile Include="MuSi\Plugin\STE\AfterFeedback\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\STE\AfterFeedback\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\STE\CustomerRecord\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\STE\CustomerRecord\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\STE\Customer\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\STE\Channel\TransferFromMuSi.cs" />
    <Compile Include="MuSi\Plugin\STE\Customer\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\STE\FollowerRecord\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\CheckTransferOrderRejectFlow.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\GetdeptByProductAuth.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\GetSuiteData.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\OMSTranster.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\TransferToMuSiOMS.cs" />
    <Compile Include="MuSi\Plugin\STK\StockOut\TransferToMuSi.cs" />
    <Compile Include="MuSi\Response\MuSiAIResponse.cs" />
    <Compile Include="MuSi\SchedulerTask\GetMSPurchaseOrderTask.cs" />
    <Compile Include="MuSi\SchedulerTask\OperationLogAutoRetryTask.cs" />
    <Compile Include="MuSi\SchedulerTask\MuSiMQEnqueueHelper.cs" />
    <Compile Include="MuSi\SchedulerTask\ReconciliationReminderTask.cs" />
    <Compile Include="MuSi\SchedulerTask\TransferFromLocalTask.cs" />
    <Compile Include="MuSi\SchedulerTask\TransferFromMuSiTask.cs" />
    <Compile Include="MuSi\SchedulerTask\MuSiMQEnqueueTask.cs" />
    <Compile Include="MuSi\SchedulerTask\TransferToMuSiMQTask.cs" />
    <Compile Include="MuSi\SchedulerTask\GetAIQuestionTask.cs" />
    <Compile Include="MuSi\SchedulerTask\TransferToMuSiOMSTask.cs" />
    <Compile Include="MuSi\SchedulerTask\TransferToMuSiTask_All.cs" />
    <Compile Include="MuSi\ServiceInst\MuSiOMSSysSyncService.cs" />
    <Compile Include="MuSi\Service\BillNoServiceExt.cs" />
    <Compile Include="MuSi\Service\GetPurOrderData.cs" />
    <Compile Include="MuSi\Service\InitAttrInfoData.cs" />
    <Compile Include="MuSi\Service\InitPriceFactor.cs" />
    <Compile Include="MuSi\Service\InitProductAuth_OtherData.cs" />
    <Compile Include="MuSi\Service\MuSiAIService_Sync.cs" />
    <Compile Include="MuSi\Service\MuSiAIService_ReceivedPubMessage.cs" />
    <Compile Include="MuSi\Service\MuSiAIService.cs" />
    <Compile Include="MuSi\Service\MuSiCrmHelper.cs" />
    <Compile Include="MuSi\Service\RepairSuitSelection.cs" />
    <Compile Include="MuSi\Utils\SignConvertUtil.cs" />
    <Compile Include="MuSi\Validation\AIBedPropMustValidation.cs" />
    <Compile Include="PDA\DeliveryScanTask\GetDeliveryTaskList.cs" />
    <Compile Include="PDA\DeliveryScanTask\ScanSubmit.cs" />
    <Compile Include="PDA\Inventoryverify\GetInventoryScanTaskList.cs" />
    <Compile Include="PDA\Inventoryverify\InventorySubmit.cs" />
    <Compile Include="PDA\Inventoryverify\QuerySourceFormId.cs" />
    <Compile Include="PDA\Inventoryverify\ScanVerification.cs" />
    <Compile Include="PDA\PdaVersion\Save.cs" />
    <Compile Include="PDA\Receptionscantask\BatchScanSubmit.cs" />
    <Compile Include="PDA\SendScanTask\BatchScanData.cs" />
    <Compile Include="PDA\SendScanTask\BatchScanSubmit.cs" />
    <Compile Include="PDA\SendScanTask\GetOutQty.cs" />
    <Compile Include="PDA\StockParam\GetStockParam.cs" />
    <Compile Include="PDA\transfertask\Delete.cs" />
    <Compile Include="PDA\transfertask\modify.cs" />
    <Compile Include="PDA\Unboxing\GetPrintDataSource.cs" />
    <Compile Include="PDA\SendScanTask\GetSendScanTaskList.cs" />
    <Compile Include="PDA\SendScanTask\SendScanSubmit.cs" />
    <Compile Include="PDA\StockQuery\GetStockData.cs" />
    <Compile Include="PDA\transfertask\GetTransfertask.cs" />
    <Compile Include="PDA\transfertask\ScanSubmit.cs" />
    <Compile Include="PDA\Unboxing\BoxingSubmit.cs" />
    <Compile Include="PDA\Unboxing\GetBarCodeInfo.cs" />
    <Compile Include="PDA\Unboxing\GetBoxingDetailData.cs" />
    <Compile Include="PDA\WmsCommon\AuditHelper.cs" />
    <Compile Include="PDA\WmsCommon\BarCodeMasterHelper.cs" />
    <Compile Include="PDA\WmsCommon\BaseDataCommon.cs" />
    <Compile Include="PDA\WmsCommon\BatchGetBarcodeInfo.cs" />
    <Compile Include="PDA\WmsCommon\CheckStaffInfo.cs" />
    <Compile Include="PDA\WmsCommon\GetBarCodeInfo.cs" />
    <Compile Include="PDA\WmsCommon\GetMaterialInfo.cs" />
    <Compile Include="PDA\Unboxing\GetTemplateList.cs" />
    <Compile Include="PDA\WmsCommon\GetUserInfo.cs" />
    <Compile Include="PDA\WmsCommon\LogHelper.cs" />
    <Compile Include="PDA\WmsCommon\SubmitHelper.cs" />
    <Compile Include="PDA\WmsCommon\SwitchOrgnization.cs" />
    <Compile Include="PDA\WmsCommon\UnitConvertHelper.cs" />
    <Compile Include="Plugin\AFT\Vist\Audit.cs" />
    <Compile Include="Plugin\AFT\Vist\Delete.cs" />
    <Compile Include="Plugin\AFT\Vist\New.cs" />
    <Compile Include="Plugin\AFT\Vist\UnAudit.cs" />
    <Compile Include="Plugin\Auth\AgentDataRule.cs" />
    <Compile Include="Plugin\Auth\CrmAgentDataRule.cs" />
    <Compile Include="Plugin\Auth\CustomerDataRule.cs" />
    <Compile Include="Plugin\Auth\DealPriceDataRule.cs" />
    <Compile Include="Plugin\Auth\PDA\GetPDAVersion.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_BigCustomerAdmin.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_BigCustomerBoss.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_BigCustomerFin.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_BigCustomerFlow.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_BigCustomerShopkeeper.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_BigCustomerSales.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_BigCustomerStock.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_BigCustomerServiceManager.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_BigCusValueServiceCommissioner.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_ValueServiceCommissioner.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_ServiceManager.cs" />
    <Compile Include="Plugin\Auth\ProductDelistingDataRule.cs" />
    <Compile Include="Plugin\Auth\PromotionDataRule.cs" />
    <Compile Include="Plugin\Auth\PurchasePriceDataRule.cs" />
    <Compile Include="Plugin\BAS\AgentLevelFilter\Save.cs" />
    <Compile Include="Plugin\BAS\AgentsParam\GetDistributorNumberAndName.cs" />
    <Compile Include="Plugin\Auth\QueryData.cs" />
    <Compile Include="Plugin\BAS\AgentsParam\Save.cs" />
    <Compile Include="Plugin\BAS\AgentsParam\ViewParam.cs" />
    <Compile Include="Plugin\BAS\Agent\ChangeAdminPhone.cs" />
    <Compile Include="Plugin\BAS\Agent\ChangeLicenseNew.cs" />
    <Compile Include="Plugin\BAS\Agent\ChangeLicense.cs" />
    <Compile Include="Plugin\BAS\Agent\ChangeLicenseAfter.cs" />
    <Compile Include="Plugin\BAS\Agent\ClearCache.cs" />
    <Compile Include="Plugin\BAS\Agent\GetAgentInfo.cs" />
    <Compile Include="Plugin\BAS\Agent\GetOrgFilter.cs" />
    <Compile Include="Plugin\BAS\Agent\GetCrmData.cs" />
    <Compile Include="Plugin\BAS\Agent\IsFirstLevelAgent.cs" />
    <Compile Include="Plugin\BAS\Agent\ListDataToExcel.cs" />
    <Compile Include="Plugin\BAS\Agent\ListDataToPdf.cs" />
    <Compile Include="Plugin\BAS\Agent\QueryData.cs" />
    <Compile Include="Plugin\BAS\Attachlist\Delete.cs" />
    <Compile Include="Plugin\BAS\Attachlist\Save.cs" />
    <Compile Include="Plugin\BAS\BankNum\AbstractQueryDyn.cs" />
    <Compile Include="Plugin\BAS\BankNum\FuzzyQuery.cs" />
    <Compile Include="Plugin\BAS\BankNum\QuerySelectorDyn.cs" />
    <Compile Include="Plugin\BAS\Brand\Save.cs" />
    <Compile Include="Plugin\BAS\CustomerInvoice\SaveCustomer.cs" />
    <Compile Include="Plugin\BAS\Deliver\GetCity.cs" />
    <Compile Include="Plugin\BAS\Dept\TransferFromMuSi.cs" />
    <Compile Include="Plugin\BAS\DistPurcchasePrice\DistPurcchasePriceService.cs" />
    <Compile Include="Plugin\BAS\DistPurcchasePrice\UpdateDisPurchasePriceData.cs" />
    <Compile Include="Plugin\BAS\FieldAstrictParam\QueryFieldAstrictParam.cs" />
    <Compile Include="PlugIn\BAS\Flow\FlowNodeTestExample.cs" />
    <Compile Include="PlugIn\BAS\Flow\FlowNodeUsageExample.cs" />
    <Compile Include="PlugIn\BAS\Flow\NodePositionInfo.cs" />
    <Compile Include="Plugin\BAS\InventoryWaterLevel\Save.cs" />
    <Compile Include="PlugIn\BAS\Flow\FlowNodeInfo.cs" />
    <Compile Include="PlugIn\BAS\Flow\FlowNodeInfoService.cs" />
    <Compile Include="Plugin\BAS\InvokeMSAuth.cs" />
    <Compile Include="Plugin\BAS\PositionAppJobMap\Save.cs" />
    <Compile Include="Plugin\BAS\PositionAppJobMap\Validation.cs" />
    <Compile Include="Plugin\BAS\Position\Save.cs" />
    <Compile Include="Plugin\BAS\PriceFactor\Save.cs" />
    <Compile Include="Plugin\BAS\PriceSchemeDetail\Delete.cs" />
    <Compile Include="Plugin\BAS\PriceSchemeDetail\UnForbid.cs" />
    <Compile Include="Plugin\BAS\PriceSchemeDetail\Forbid.cs" />
    <Compile Include="Plugin\BAS\PriceSchemeDetail\Save.cs" />
    <Compile Include="Plugin\BAS\PriceSchemeDetail\SaveValidation.cs" />
    <Compile Include="Plugin\BAS\PriceScheme\Delete.cs" />
    <Compile Include="Plugin\BAS\PriceScheme\Forbid.cs" />
    <Compile Include="Plugin\BAS\PriceScheme\HasDetail.cs" />
    <Compile Include="Plugin\BAS\PriceScheme\Save.cs" />
    <Compile Include="Plugin\BAS\PriceScheme\SaveValidation.cs" />
    <Compile Include="Plugin\BAS\PriceScheme\UnForbid.cs" />
    <Compile Include="Plugin\BAS\Price\AbstractQueryDyn.cs" />
    <Compile Include="Plugin\BAS\Price\FuzzyQuery.cs" />
    <Compile Include="Plugin\BAS\Price\GetPriceFactor.cs" />
    <Compile Include="Plugin\BAS\Price\ImportExcelData.cs" />
    <Compile Include="Plugin\BAS\PrintTmpl\Delete.cs" />
    <Compile Include="Plugin\BAS\ProductAuth\AbstractQueryDyn.cs" />
    <Compile Include="Plugin\BAS\ProductAuth\ClearExclude.cs" />
    <Compile Include="Plugin\BAS\ProductAuth\ClearZGProduct_other.cs" />
    <Compile Include="Plugin\BAS\ProductAuth\UnForbid.cs" />
    <Compile Include="Plugin\BAS\ProductAuth\Forbid.cs" />
    <Compile Include="Plugin\BAS\ProductAuth\FuzzyQuery.cs" />
    <Compile Include="Plugin\BAS\ProductAuth\QueryProduct.cs" />
    <Compile Include="Plugin\BAS\ProductAuth\QuerySelector.cs" />
    <Compile Include="Plugin\BAS\ProductAuth\SubmitZGProduct_other.cs" />
    <Compile Include="Plugin\BAS\ProductDelisting\CheckDelisting.cs" />
    <Compile Include="Plugin\BAS\ProductDelisting\Modify.cs" />
    <Compile Include="Plugin\BAS\ProductDelisting\ProductDelistingPush.cs" />
    <Compile Include="Plugin\BAS\ProductDelisting\QueryData.cs" />
    <Compile Include="Plugin\BAS\ProductDelisting\Refresh.cs" />
    <Compile Include="Plugin\BAS\ProductDelisting\RetryUpdate.cs" />
    <Compile Include="Plugin\BAS\Product\CheckBarCodeRule.cs" />
    <Compile Include="Plugin\BAS\Product\ClearCache.cs" />
    <Compile Include="Plugin\BAS\ProductAuth\ClearZGProduct.cs" />
    <Compile Include="Plugin\BAS\ProductAuth\SubmitZGProduct.cs" />
    <Compile Include="Plugin\BAS\Product\GoodList.cs" />
    <Compile Include="Plugin\BAS\Product\Validation_Save.cs" />
    <Compile Include="Plugin\BAS\PromotionCombine\Modify.cs" />
    <Compile Include="Plugin\BAS\PromotionCombine\QueryData.cs" />
    <Compile Include="Plugin\BAS\PromotionCombine\Refresh.cs" />
    <Compile Include="Plugin\BAS\PromotionScheme\AbstractQueryDyn.cs" />
    <Compile Include="Plugin\BAS\PromotionScheme\FuzzyQuery.cs" />
    <Compile Include="Plugin\BAS\PromotionScheme\Modify.cs" />
    <Compile Include="Plugin\BAS\PromotionScheme\QueryData.cs" />
    <Compile Include="Plugin\BAS\PromotionScheme\Refresh.cs" />
    <Compile Include="Plugin\BAS\RePrice\RePriceDataRule.cs" />
    <Compile Include="Plugin\BAS\Series\GetBrandById.cs" />
    <Compile Include="Plugin\BAS\Series\GetIsMusiFlag.cs" />
    <Compile Include="Plugin\BAS\Series\UpdateStroeNewChannel.cs" />
    <Compile Include="Plugin\BAS\Staff\GenerateUser.cs" />
    <Compile Include="Plugin\BAS\Store\AbstractQueryDyn.cs" />
    <Compile Include="Plugin\BAS\Store\CheckVisitPermission.cs" />
    <Compile Include="Plugin\BAS\Store\Delete.cs" />
    <Compile Include="Plugin\BAS\Store\Forbid.cs" />
    <Compile Include="Plugin\BAS\Store\AddProductAuth.cs" />
    <Compile Include="Plugin\BAS\Store\FuzzyQuery.cs" />
    <Compile Include="Plugin\BAS\Store\QueryData.cs" />
    <Compile Include="Plugin\BAS\Store\Save.cs" />
    <Compile Include="Plugin\BAS\Store\StoreHelper.cs" />
    <Compile Include="Plugin\BAS\Store\TransferAdmin.cs" />
    <Compile Include="Plugin\BAS\Store\TransferConfirm.cs" />
    <Compile Include="Plugin\BAS\Store\Transfer.cs" />
    <Compile Include="Plugin\BAS\Store\UnForbid.cs" />
    <Compile Include="Plugin\BAS\BizobjectFieldMap\ImportBase.cs" />
    <Compile Include="Plugin\BAS\City\GetCityByFormInfo.cs" />
    <Compile Include="Plugin\BAS\CustomerContact\Save.cs" />
    <Compile Include="Plugin\BAS\DeliveryFactory\GetOrgType.cs" />
    <Compile Include="Plugin\BAS\MainAgentConfig\Audit.cs" />
    <Compile Include="Plugin\BAS\Org\Forbid.cs" />
    <Compile Include="Plugin\BAS\Price\GetProductSallPrice.cs" />
    <Compile Include="Plugin\BAS\BasTask\ImportBase.cs" />
    <Compile Include="Plugin\BCM\BarcodeMaster\Archiving.cs" />
    <Compile Include="Plugin\BCM\BarcodeMaster\printtmpl.cs" />
    <Compile Include="Plugin\BCM\BarcodeMaster\ReceiveAmount.cs" />
    <Compile Include="Plugin\BCM\BarcodeMaster\SearchBarcode.cs" />
    <Compile Include="Plugin\BCM\CommonDelete.cs" />
    <Compile Include="Plugin\BCM\CommonSave.cs" />
    <Compile Include="Plugin\BCM\PackOrder\CreatePackageInit.cs" />
    <Compile Include="Plugin\BCM\PackOrder\Submit.cs" />
    <Compile Include="Plugin\BCM\ReceptionScanTask\Cancel.cs" />
    <Compile Include="Plugin\BCM\ReceptionScanTask\Draft.cs" />
    <Compile Include="Plugin\BCM\ReceptionScanTask\InStock.cs" />
    <Compile Include="Plugin\BCM\ReceptionScanTask\LinkFormSearch.cs" />
    <Compile Include="Plugin\BCM\ReceptionScanTask\LoadInStockData.cs" />
    <Compile Include="Plugin\BCM\ReceptionScanTask\Query.cs" />
    <Compile Include="Plugin\BCM\ReceptionScanTask\QueryFieldsPermission.cs" />
    <Compile Include="Plugin\BCM\ReceptionScanTask\UnCancel.cs" />
    <Compile Include="Plugin\BCM\ScanTask\CompleteTask.cs" />
    <Compile Include="Plugin\BCM\ScanTask\Delete.cs" />
    <Compile Include="Plugin\BCM\ScanTask\LoadSupplier.cs" />
    <Compile Include="Plugin\BCM\ScanTask\QuerySelector.cs" />
    <Compile Include="Plugin\BCM\ScanTask\Save.cs" />
    <Compile Include="Plugin\BCM\TransferInTask\QueryData.cs" />
    <Compile Include="Plugin\BCM\Transfertask\QueryData.cs" />
    <Compile Include="Plugin\BD\Attachlist\Save.cs" />
    <Compile Include="Plugin\BD\BillType\GetUiBillTypeParam.cs" />
    <Compile Include="Plugin\BD\BillType\Save.cs" />
    <Compile Include="Plugin\BillConvert\Inventorytransfer2PackOrderConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\Order2OrderApplyChgConvertPlugln.cs" />
    <Compile Include="Plugin\BillConvert\PurchaseOrder2AfterfeedbackConvertPlugln.cs" />
    <Compile Include="Plugin\BillConvert\PurchaseOrder2PurchaseOrderApplyChg.cs" />
    <Compile Include="Plugin\BillConvert\ReceptionScanTask2PackOrderConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\SalOrder2OutStockAutoConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\SalOrder2SalOrderConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\SostockOut2CollectreceiptPlugIn.cs" />
    <Compile Include="Plugin\CommonOperateCtrl.cs" />
    <Compile Include="Plugin\FIN\ChargeDialog\GetCusacount.cs" />
    <Compile Include="Plugin\FIN\ChargeDialog\GetDeptByStaff.cs" />
    <Compile Include="Plugin\FIN\CooSettle\CheckAmount.cs" />
    <Compile Include="Plugin\FIN\CooSettle\CheckInvoiceNumber.cs" />
    <Compile Include="Plugin\FIN\CostCalc\New.cs" />
    <Compile Include="Plugin\FIN\CostCalc\StockCostCalcAll.cs" />
    <Compile Include="Plugin\FIN\CostCalc\StockCostCalc.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\AuditFlow.cs" />
    <Compile Include="PlugIn\FIN\IncomeDisburse\CooCancel.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\CheckSourceOrder.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\GetDeptByStaff.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\Draft.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\IncomePushHeadquart.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\SaveValidation.cs" />
    <Compile Include="PlugIn\FIN\IncomeDisburse\Submit.cs" />
    <Compile Include="PlugIn\FIN\IncomeDisburse\SubmitFlow.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\UnAuditValidation.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\AuditValidation.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\IncomeDisburseHelper.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\UnSubmit.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\Validation_Check.cs" />
    <Compile Include="Plugin\FIN\Logisticsstatement\Cancel.cs" />
    <Compile Include="PlugIn\FIN\Registfee\AuditFlow.cs" />
    <Compile Include="PlugIn\FIN\Registfee\Copy.cs" />
    <Compile Include="Plugin\FIN\Registfee\LoadOrderPrice.cs" />
    <Compile Include="PlugIn\FIN\Registfee\RegistfeeSubmitHQ.cs" />
    <Compile Include="PlugIn\FIN\Registfee\Submit.cs" />
    <Compile Include="PlugIn\FIN\Registfee\SubmitFlow.cs" />
    <Compile Include="PlugIn\FIN\Registfee\TransferToMuSi.cs" />
    <Compile Include="PlugIn\FIN\Registfee\UnSubmit.cs" />
    <Compile Include="PlugIn\FIN\Registfee\Validation_Check.cs" />
    <Compile Include="Plugin\Musi\SubsidyOrderSyncConfig\GetDefaultBizForm.cs" />
    <Compile Include="Plugin\Musi\SubsidyOrderSyncConfig\Save.cs" />
    <Compile Include="Plugin\Musi\SubsidyOrderSyncConfig\Validation_Save.cs" />
    <Compile Include="Plugin\Pro\Maintenance\Active.cs" />
    <Compile Include="Plugin\Pro\Maintenance\ArgueQestionConfirm.cs" />
    <Compile Include="Plugin\Pro\Maintenance\BaseCommon.cs" />
    <Compile Include="Plugin\Pro\Maintenance\BusinessConfirm.cs" />
    <Compile Include="Plugin\Pro\Maintenance\CustomerVerified.cs" />
    <Compile Include="Plugin\Pro\Maintenance\DataConfirm.cs" />
    <Compile Include="Plugin\Pro\Maintenance\DataProvide.cs" />
    <Compile Include="Plugin\Pro\Maintenance\DataRepairFinish.cs" />
    <Compile Include="Plugin\Pro\Maintenance\Delete.cs" />
    <Compile Include="Plugin\Pro\Maintenance\EndAnalysis.cs" />
    <Compile Include="Plugin\Pro\Maintenance\GetDefaultDeveloper.cs" />
    <Compile Include="Plugin\Pro\Maintenance\GetShouldDoneDate.cs" />
    <Compile Include="Plugin\Pro\Maintenance\GetShouldResponseTime.cs" />
    <Compile Include="Plugin\Pro\Maintenance\HasRelease.cs" />
    <Compile Include="Plugin\Pro\Maintenance\InternalDisputeConfirm.cs" />
    <Compile Include="Plugin\Pro\Maintenance\OperationsReject.cs" />
    <Compile Include="Plugin\Pro\Maintenance\OperationsVerified.cs" />
    <Compile Include="Plugin\Pro\Maintenance\Query.cs" />
    <Compile Include="Plugin\Pro\Maintenance\QueryData.cs" />
    <Compile Include="Plugin\Pro\Maintenance\QuestionClose.cs" />
    <Compile Include="Plugin\Pro\Maintenance\Save.cs" />
    <Compile Include="Plugin\Pro\Maintenance\SchemeChangeFinish.cs" />
    <Compile Include="Plugin\Pro\Maintenance\SchemeConfirm.cs" />
    <Compile Include="Plugin\Pro\Maintenance\SchemeFinish.cs" />
    <Compile Include="Plugin\Pro\Maintenance\StartAnalysis.cs" />
    <Compile Include="Plugin\Pro\Maintenance\SubmitOpex.cs" />
    <Compile Include="Plugin\Pro\Maintenance\ToBeObserved.cs" />
    <Compile Include="Plugin\Pro\Maintenance\ToDeveloper.cs" />
    <Compile Include="Plugin\Pro\ProblemEfficiencyAnalysis\QueryListReportData.cs" />
    <Compile Include="Plugin\Pro\ProblemProgressAnalysis\QueryListReportData.cs" />
    <Compile Include="Plugin\Pur\HqSubPurOrder\SubmitPur.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrderApplyChg\Save.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\AuditValidation.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\CanChangeDeliverId.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\CheckPromotion.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\GetAlterEntry.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\CreateReceptionScanTask.cs" />
    <Compile Include="Plugin\BCM\ScanResult\QueryData.cs" />
    <Compile Include="Plugin\BCM\Transfertask\Delete.cs" />
    <Compile Include="Plugin\BillConvert\Afterfeedback2ServiceConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\Customer2AfterfeedbackConvertPlugln.cs" />
    <Compile Include="Plugin\BillConvert\Customer2ServiceConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\InventoryVerify2CountScanTaskConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\Order2AfterfeedbackConvertPlugln.cs" />
    <Compile Include="Plugin\BillConvert\OtherStockIn2ReceptionScanTaskConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\OtherStockOut2DeliveryScantaskConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\OtherStockIn2PackOrderConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\PostockReturn2DeliveryScantaskConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\PurchaseOrder2PackOrderConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\Service2AfterfeedbackConvertPlugln.cs" />
    <Compile Include="Plugin\BillConvert\SostockOut2DeliveryScanTaskConvert.cs" />
    <Compile Include="Plugin\BillConvert\Sostockout2ServiceConvertPlugIn.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\Validation_CheckExcess.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Draft.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\GetDeliverBySerieid.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\GetHqordernoByFormInfo.cs" />
    <Compile Include="Plugin\DeductionReturnQty.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\GetPurStoreCity.cs" />
    <Compile Include="Plugin\BillConvert\PurchaseOrder2ReceptionScantaskConvertPlugIn.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\GetSalePromotionInfo.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\HQOrderStatusConst.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\InitiateChangeApply.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\IsTopOrgid.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\OrderToPurchase_SWJ_OrderConvertService.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Pull.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\PushFeedBack.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\PushReceptionScanTask.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\QueryInventory.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SaveCombineInfo.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\ShowCombineInfo.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\ShowPurChaseOrder.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\StockUp.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SubmitAgentValidation.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SubmitAgent.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SubmitValidation.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SwingField.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\UnCancel.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\UnChange.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\UnSubmit.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Validation_CheckPromotion.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Validation_Product.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Validation_ChannelType.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Validation_Save.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Writeback.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\Audit.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\BaseValidationPlugin.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\Modify.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\Push.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\SaveValidation.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\Submit.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\UnAudit.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\UnSubmit.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\ValidationOrgOperation.cs" />
    <Compile Include="Plugin\Pur\PurSysParam\LoadPurSysParam.cs" />
    <Compile Include="Plugin\Sal\ClosedAccounts\AntiClosing.cs" />
    <Compile Include="Plugin\Sal\ClosedAccounts\Closing.cs" />
    <Compile Include="Plugin\Sal\ClosedAccounts\New.cs" />
    <Compile Include="Plugin\Sal\ClosedAccounts\QueryData.cs" />
    <Compile Include="Plugin\Sal\ClosedAccounts\Refresh.cs" />
    <Compile Include="PlugIn\Sal\Collectreceipt\GetOrderInfo.cs" />
    <Compile Include="Plugin\Sal\Collectreceipt\LinkFormSearch.cs" />
    <Compile Include="Plugin\Sal\Collectreceipt\LoadSettleInfo.cs" />
    <Compile Include="Plugin\Sal\Collectreceipt\NewSettle.cs" />
    <Compile Include="Plugin\Sal\Collectreceipt\Save.cs" />
    <Compile Include="Plugin\Sal\HistoryBill\Modify.cs" />
    <Compile Include="Plugin\Sal\HistoryBill\QueryData.cs" />
    <Compile Include="Plugin\Sal\HistoryBill\SelectFirstOrderTime.cs" />
    <Compile Include="Plugin\Sal\Promotion\ComboPromotion\QueryData.cs" />
    <Compile Include="Plugin\Sal\Promotion\ComboPromotion\Save.cs" />
    <Compile Include="Plugin\Sal\Promotion\ComboPromotion\SaveValidation.cs" />
    <Compile Include="Plugin\Sal\Promotion\ProductPromotion\New.cs" />
    <Compile Include="Plugin\Sal\Promotion\ProductPromotion\SaveValidation.cs" />
    <Compile Include="Plugin\Sal\Promotion\ProductPromotion\Submit.cs" />
    <Compile Include="Plugin\Sal\Promotion\QueryListReportData.cs" />
    <Compile Include="Plugin\Sal\Promotion\SelectProductPromotionDialog\New.cs" />
    <Compile Include="Plugin\Sal\Promotion\UnPublish.cs" />
    <Compile Include="Plugin\Sal\Promotion\Publish.cs" />
    <Compile Include="Plugin\Sal\Promotion\ProductPromotion\Save.cs" />
    <Compile Include="Plugin\Sec\ChangePhone.cs" />
    <Compile Include="Plugin\Sec\Delete.cs" />
    <Compile Include="Plugin\Sec\GetCurrentOrgWarnUser.cs" />
    <Compile Include="Plugin\Sec\GetIsWarnUser.cs" />
    <Compile Include="Plugin\Sec\ModifyPassword.cs" />
    <Compile Include="Plugin\Sec\QueryControlFieldPermission.cs" />
    <Compile Include="Plugin\Sec\ResetPassword.cs" />
    <Compile Include="Plugin\Sec\SaveRolePermission.cs" />
    <Compile Include="Plugin\Sec\SendModifyPwd.cs" />
    <Compile Include="Plugin\Sec\UnForbid.cs" />
    <Compile Include="Plugin\Sec\Validation_Import.cs" />
    <Compile Include="Plugin\Sel\Promotion\Save.cs" />
    <Compile Include="Plugin\Sel\Prop\Delete.cs" />
    <Compile Include="Plugin\Sel\Selection\AddPromotion.cs" />
    <Compile Include="Plugin\SER\Service\AbstractQueryDyn.cs" />
    <Compile Include="Plugin\SER\Service\CancelDelivery.cs" />
    <Compile Include="Plugin\SER\Service\Cancelservice.cs" />
    <Compile Include="Plugin\SER\Service\ConfirmDelivery.cs" />
    <Compile Include="Plugin\SER\Service\FuzzyQuery.cs" />
    <Compile Include="Plugin\SER\Service\GetServiceItem.cs" />
    <Compile Include="Plugin\SER\Service\GetServicetypeBySeritemid.cs" />
    <Compile Include="Plugin\SER\Service\GetVistListBySer.cs" />
    <Compile Include="Plugin\SER\Service\New.cs" />
    <Compile Include="Plugin\SER\Service\Pushaftermanager.cs" />
    <Compile Include="Plugin\SER\Service\QueryData.cs" />
    <Compile Include="Plugin\SER\Service\QuerySelectorDyn.cs" />
    <Compile Include="Plugin\SER\Service\QueryServiceOrderBillType.cs" />
    <Compile Include="Plugin\SER\Service\Refresh.cs" />
    <Compile Include="Plugin\SER\Service\ReturnBack.cs" />
    <Compile Include="Plugin\SER\Service\ServiceClose.cs" />
    <Compile Include="Plugin\SER\Service\Servicevist.cs" />
    <Compile Include="Plugin\SER\Service\Transfer.cs" />
    <Compile Include="Plugin\SER\Service\UnCancel.cs" />
    <Compile Include="Plugin\SER\Service\Validation_CancelService.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\Accept.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\Cancel.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\Delete.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\Fclose.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\FeedBackVist.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\Finish.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\GetFeedBackInfo.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\LinkFormSearch.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\ListAttachment.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\LoadSettleInfo.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\New.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\Pull.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\push2poreturn.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\QueryPullSource.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\QuerySelector.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\ReturnSend.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\Save.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\Validation_Save.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\Settle.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\SubmitHQ.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\Transfer.cs" />
    <Compile Include="Plugin\STE\AfterFeedback\UnCancels.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\CommonCus.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\MuSiSyncData.cs" />
    <Compile Include="Plugin\STE\Customer\ChangeCustomerFieldEvent.cs" />
    <Compile Include="Plugin\STE\Customer\CheckPhone.cs" />
    <Compile Include="Plugin\STE\Customer\CommonCus.cs" />
    <Compile Include="Plugin\STE\Customer\DistributeDuty.cs" />
    <Compile Include="Plugin\STE\Customer\Draft.cs" />
    <Compile Include="Plugin\STE\Customer\GetContactByCustomer.cs" />
    <Compile Include="Plugin\STE\Customer\SaveValidation.cs" />
    <Compile Include="Plugin\STE\Customer\PhoneChangeWork.cs" />
    <Compile Include="Plugin\STE\Customer\SaveValidationByCusContact.cs" />
    <Compile Include="Plugin\STE\Customer\ThreedDesign.cs" />
    <Compile Include="Plugin\STE\Customer\UnForbid.cs" />
    <Compile Include="PlugIn\STE\MarketStatement\AuditFlow.cs" />
    <Compile Include="PlugIn\STE\MarketStatement\Copy.cs" />
    <Compile Include="PlugIn\STE\MarketStatement\GetMarketStatementInfo.cs" />
    <Compile Include="Plugin\STE\MarketStatement\MKSubmitHQ.cs" />
    <Compile Include="Plugin\STE\MarketStatement\Submit.cs" />
    <Compile Include="PlugIn\STE\MarketStatement\SubmitFlow.cs" />
    <Compile Include="Plugin\STE\MarketStatement\TransferToMuSi.cs" />
    <Compile Include="Plugin\STE\MarketStatement\UnSubmit.cs" />
    <Compile Include="Plugin\STE\MarketStatement\Validation_Check.cs" />
    <Compile Include="Plugin\STE\OrderApplyChg\Audit.cs" />
    <Compile Include="Plugin\STE\OrderApplyChg\OrderApplyChgRejected.cs" />
    <Compile Include="Plugin\STE\OrderSettleDyn\Validation_Settle.cs" />
    <Compile Include="Plugin\STE\Order\AuditThe.cs" />
    <Compile Include="Plugin\STE\Order\CheckAfterSaleOrder.cs" />
    <Compile Include="Plugin\STE\Order\CheckCanPushRegistFee.cs" />
    <Compile Include="Plugin\STE\Order\CheckProductSeriesIsNewChannel.cs" />
    <Compile Include="Plugin\STE\Order\CheckProducts.cs" />
    <Compile Include="Plugin\STE\Order\CheckStore.cs" />
    <Compile Include="Plugin\STE\Order\CheckSubmitOMS.cs" />
    <Compile Include="Plugin\STE\Order\CheckTransferOrder.cs" />
    <Compile Include="Plugin\BAS\Price\GetInventoryProductPrice.cs" />
    <Compile Include="Plugin\STE\Order\CheckWithIn.cs" />
    <Compile Include="Plugin\STE\Order\CopyOrder.cs" />
    <Compile Include="PlugIn\STE\Order\DirectOrder\PushShareCostBillNo.cs" />
    <Compile Include="PlugIn\STE\Order\DirectOrder\ReturnHeadQuarterStatus.cs" />
    <Compile Include="Plugin\STE\Order\DropShipment.cs" />
    <Compile Include="Plugin\STE\Order\E3\E3Commom.cs" />
    <Compile Include="Plugin\STE\Order\E3\E3PublicPlugin.cs" />
    <Compile Include="Plugin\STE\Order\GetAgentCanQueryInventory.cs" />
    <Compile Include="Plugin\STE\Order\GetAgentCustomChannel.cs" />
    <Compile Include="Plugin\STE\Order\GetDirectSaleGiveAwayIsNotZeroParam.cs" />
    <Compile Include="Plugin\STE\Order\GetIndentType.cs" />
    <Compile Include="Plugin\STE\Order\GetOMSConfig.cs" />
    <Compile Include="Plugin\STE\Order\GetOrderData.cs" />
    <Compile Include="Plugin\STE\Order\GetOrderLogisticsProgress.cs" />
    <Compile Include="Plugin\STE\Order\GetOrderVersion.cs" />
    <Compile Include="Plugin\STE\Order\GetPartsListQueryUrl.cs" />
    <Compile Include="Plugin\STE\Order\GetRenewTypeById.cs" />
    <Compile Include="Plugin\STE\Order\GetSourceAfterOrder.cs" />
    <Compile Include="Plugin\STE\Order\GetSWJRowData.cs" />
    <Compile Include="Plugin\STE\Order\GetViewFactoryOrderApprovalLogUrl.cs" />
    <Compile Include="Plugin\STE\Order\OrderFileSave.cs" />
    <Compile Include="Plugin\STE\Order\OrderProcess.cs" />
    <Compile Include="Plugin\STE\Order\OrderRecRefund.cs" />
    <Compile Include="Plugin\STE\Order\PieceSendOrderPushSoStock.cs" />
    <Compile Include="Plugin\STE\Order\Prjreturn.cs" />
    <Compile Include="Plugin\STE\Order\prjconfirm.cs" />
    <Compile Include="Plugin\STE\Order\CheckEditPrice.cs" />
    <Compile Include="Plugin\STE\Order\CheckChangeBill.cs" />
    <Compile Include="Plugin\STE\Order\CheckHasHqChg.cs" />
    <Compile Include="Plugin\STE\Order\CheckHasIncomeDisburse.cs" />
    <Compile Include="Plugin\STE\Order\CheckSwjOrder.cs" />
    <Compile Include="Plugin\STE\Order\GetOrderEntryByNo.cs" />
    <Compile Include="Plugin\STE\Order\FillPromotionData.cs" />
    <Compile Include="Plugin\STE\Order\GetPrintDataSource.cs" />
    <Compile Include="Plugin\STE\Order\ListAttachment.cs" />
    <Compile Include="Plugin\STE\Order\Push2Order.cs" />
    <Compile Include="Plugin\STE\Order\PushPurOrder_YJDF.cs" />
    <Compile Include="Plugin\STE\Order\PushService.cs" />
    <Compile Include="Plugin\STE\Order\QueryOrderBillType.cs" />
    <Compile Include="Plugin\STE\Order\RenewalRefund.cs" />
    <Compile Include="Plugin\STE\Order\RenewalReceipt.cs" />
    <Compile Include="Plugin\STE\Order\RenewalPur.cs" />
    <Compile Include="Plugin\STE\Order\SubmitAfterSale.cs" />
    <Compile Include="Plugin\STE\Order\SubmitHeadQuart.cs" />
    <Compile Include="Plugin\STE\Order\SubmitOMSAgain.cs" />
    <Compile Include="Plugin\STE\Order\TgcPartByStandard.cs" />
    <Compile Include="Plugin\STE\Order\CheckTgcPart.cs" />
    <Compile Include="Plugin\STE\Order\Common\ResellerHelper.cs" />
    <Compile Include="Plugin\STE\Order\Draft.cs" />
    <Compile Include="Plugin\STE\Order\GetBillTypeParam.cs" />
    <Compile Include="Plugin\STE\Order\PushPurchaseorder_SWJ.cs" />
    <Compile Include="Plugin\STE\Order\UnTransferShip.cs" />
    <Compile Include="Plugin\STE\Order\GetHqDesigner.cs" />
    <Compile Include="Plugin\STE\Order\IsPushSostockOut.cs" />
    <Compile Include="Plugin\STE\Order\OrderPushSoStock.cs" />
    <Compile Include="Plugin\STE\Order\SubmitThe.cs" />
    <Compile Include="Plugin\STE\Order\TransferShip.cs" />
    <Compile Include="Plugin\STE\Order\UpdateLogisticsProgress.cs" />
    <Compile Include="Plugin\STE\Order\UpdatePrice.cs" />
    <Compile Include="Plugin\STE\Order\UpdateReserveBill.cs" />
    <Compile Include="Plugin\BCM\BarcodeMaster\IsExsitBarcode.cs" />
    <Compile Include="Plugin\STE\Order\Validation_AfterSaleOrder.cs" />
    <Compile Include="Plugin\STE\Order\Validation_Change.cs" />
    <Compile Include="Plugin\STE\Order\Validation_Renew.cs" />
    <Compile Include="Plugin\STE\Order\Validation_InventoryTransfer.cs" />
    <Compile Include="Plugin\STE\Order\Validation_Cancel.cs" />
    <Compile Include="Plugin\STE\Order\Validation_ReserveInfo.cs" />
    <Compile Include="Plugin\STE\Order\Validation_Submit.cs" />
    <Compile Include="Plugin\STE\Order\Validation_SubmitChange.cs" />
    <Compile Include="Plugin\STE\Order\Validation_SubmitHeadQuart.cs" />
    <Compile Include="Plugin\STE\Order\Validation_UnChange.cs" />
    <Compile Include="Plugin\STE\Order\Writeback.cs" />
    <Compile Include="Plugin\STE\StoreSysParam\Save.cs" />
    <Compile Include="Plugin\STE\StoreSysParam\Validation_Save.cs" />
    <Compile Include="Plugin\STK\CommonAudit.cs" />
    <Compile Include="Plugin\STK\CommonUnAudit.cs" />
    <Compile Include="Plugin\STK\DeliveryNotice\Audit.cs" />
    <Compile Include="Plugin\STK\DeliveryNotice\UnAudit.cs" />
    <Compile Include="Plugin\STK\CommonSave.cs" />
    <Compile Include="Plugin\STK\GetFIFOStock.cs" />
    <Compile Include="Plugin\STK\InventoryBase\Save.cs" />
    <Compile Include="Plugin\STK\InventoryBase\Submit.cs" />
    <Compile Include="Plugin\STK\InventoryList\DefaultFilterSchema.cs" />
    <Compile Include="Plugin\STK\InventoryList\InventoryHelper.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\Audit.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\AuditFlow.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\PackInit.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\ReturnHeadQuarterStatus.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\SetIniStockBal.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\Pack.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\printpybarcode.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\Save.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\Delete.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\Submit.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\SubmitHeadQuart.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\TerminateFlow.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\UnAudit.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\UnSubmit.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\Validation_ZYCheck.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\VerifyProduct.cs" />
    <Compile Include="Plugin\STK\OtherStockIn\Audit.cs" />
    <Compile Include="Plugin\STK\OtherStockIn\CreateRecScanTask.cs" />
    <Compile Include="Plugin\STK\OtherStockIn\QueryInventory.cs" />
    <Compile Include="Plugin\STK\OtherStockIn\Save.cs" />
    <Compile Include="Plugin\STK\OtherStockIn\UnSubmit.cs" />
    <Compile Include="Plugin\STK\OtherStockOut\Audit.cs" />
    <Compile Include="Plugin\STK\OtherStockOut\Cancel.cs" />
    <Compile Include="Plugin\STK\OtherStockOut\CreateScanTask.cs" />
    <Compile Include="Plugin\STK\OtherStockOut\Delete.cs" />
    <Compile Include="Plugin\STK\OtherStockOut\LinkFormSearch.cs" />
    <Compile Include="Plugin\STK\OtherStockOut\QueryInventory.cs" />
    <Compile Include="Plugin\STK\OtherStockOut\Save.cs" />
    <Compile Include="Plugin\STK\OtherStockOut\UnAudit.cs" />
    <Compile Include="Plugin\STK\OtherStockOut\UnCancel.cs" />
    <Compile Include="Plugin\STK\OtherStockOut\UnSubmit.cs" />
    <Compile Include="Plugin\STK\OtherStockOut\UpdateReserveBill.cs" />
    <Compile Include="Plugin\STK\PoStockIn\Delete.cs" />
    <Compile Include="Plugin\STK\PoStockIn\DeleteValidation.cs" />
    <Compile Include="Plugin\STK\PoStockIn\Draft.cs" />
    <Compile Include="Plugin\STK\PoStockIn\IsHasBarCodeMaster.cs" />
    <Compile Include="Plugin\STK\PoStockIn\LinkFormSearch.cs" />
    <Compile Include="Plugin\STK\PoStockIn\Query.cs" />
    <Compile Include="Plugin\STK\PoStockIn\QueryFieldsPermission.cs" />
    <Compile Include="Plugin\STK\PoStockIn\QueryLock.cs" />
    <Compile Include="Plugin\STK\PoStockIn\QueryPrice.cs" />
    <Compile Include="Plugin\STK\PoStockIn\SaveEditInfo.cs" />
    <Compile Include="Plugin\STK\PoStockIn\UnCancel.cs" />
    <Compile Include="Plugin\STK\PoStockReturn\CreateScanTask.cs" />
    <Compile Include="Plugin\STK\PoStockReturn\Pull.cs" />
    <Compile Include="Plugin\STK\PoStockReturn\QueryLock.cs" />
    <Compile Include="Plugin\STK\PoStockReturn\QuerySelector.cs" />
    <Compile Include="Plugin\STK\PoStockReturn\Save.cs" />
    <Compile Include="Plugin\STK\PoStockReturn\UnSubmit.cs" />
    <Compile Include="Plugin\STK\PurReceiptNotice\Audit.cs" />
    <Compile Include="Plugin\STK\PurReceiptNotice\UnAudit.cs" />
    <Compile Include="Plugin\STK\SoStockOut\CreateScanTask.cs" />
    <Compile Include="Plugin\STK\SoStockOut\DeleteValidation.cs" />
    <Compile Include="Plugin\STK\SoStockOut\QueryAutoNewServiceParam.cs" />
    <Compile Include="Plugin\STK\SoStockOut\QueryById.cs" />
    <Compile Include="Plugin\STK\SoStockOut\QueryEntryWareHouseType.cs" />
    <Compile Include="Plugin\STK\SoStockOut\QueryPrice.cs" />
    <Compile Include="Plugin\STK\SoStockOut\SaveEditInfo.cs" />
    <Compile Include="Plugin\STK\SoStockOut\UnSubmit.cs" />
    <Compile Include="Plugin\STK\SoStockOut\UpdateReserveBill.cs" />
    <Compile Include="Plugin\STK\SoStockOut\ZYPublic.cs" />
    <Compile Include="Plugin\STK\SoStockReturn\Audit.cs" />
    <Compile Include="Plugin\STK\SoStockReturn\CheckReturnOrder.cs" />
    <Compile Include="Plugin\STK\SoStockReturn\LoadSettleInfo.cs" />
    <Compile Include="Plugin\STK\SoStockReturn\NewSettle.cs" />
    <Compile Include="Plugin\STK\SoStockReturn\Pull.cs" />
    <Compile Include="Plugin\STK\SoStockReturn\QuerySelector.cs" />
    <Compile Include="Plugin\STK\SoStockReturn\Save.cs" />
    <Compile Include="Plugin\STK\SoStockReturn\Submit.cs" />
    <Compile Include="Plugin\STK\SoStockReturn\UnAudit.cs" />
    <Compile Include="Plugin\STK\SoStockReturn\UnSubmit.cs" />
    <Compile Include="Plugin\STK\StockStatus\Save.cs" />
    <Compile Include="Plugin\STK\StockStatus\Validation_Name.cs" />
    <Compile Include="Plugin\STK\StoreHouse\GetCalculate.cs" />
    <Compile Include="Plugin\STK\StoreHouse\Validation_Inv.cs" />
    <Compile Include="Plugin\Sys\RecordList\Query.cs" />
    <Compile Include="Plugin\Sys\SmsTemplate\AddSmsTemplate.cs" />
    <Compile Include="Plugin\Sys\SmsTemplate\AlibabaCloudUtil.cs" />
    <Compile Include="Plugin\Sys\SmsTemplate\Delete.cs" />
    <Compile Include="Plugin\Sys\SmsTemplate\QuerySmsTemplate.cs" />
    <Compile Include="Plugin\Sys\SmsTemplate\SmsPreset.cs" />
    <Compile Include="PriceCalculateService.cs" />
    <Compile Include="ProductmonthlySalesService.cs" />
    <Compile Include="Report\AgentStockSummary\AgentStockSumService.cs" />
    <Compile Include="Report\AgentStockSummary\gettopcategory.cs" />
    <Compile Include="Report\AgentStockSummary\QueryData.cs" />
    <Compile Include="Report\AgentStockSummary\QueryListReport.cs" />
    <Compile Include="Report\AgentStockSummary\UpdateData.cs" />
    <Compile Include="Report\CategorystockDetailmary\QueryListReport.cs" />
    <Compile Include="Report\CategorystockDetailmary\QueryListReportData.cs" />
    <Compile Include="Report\CategorystockSummary\QueryListReport.cs" />
    <Compile Include="Report\CategorystockSummary\QueryListReportData.cs" />
    <Compile Include="Report\CenterStockSummary\gettopcategory.cs" />
    <Compile Include="Report\CenterStockSummary\QueryListReport.cs" />
    <Compile Include="Report\CenterStockSummary\QueryListReportData.cs" />
    <Compile Include="Report\CompleteSetPercent\QueryListReportData.cs" />
    <Compile Include="Report\CooIncomedisburse\OperAudit.cs" />
    <Compile Include="Report\CooIncomedisburse\OperSubmit.cs" />
    <Compile Include="Report\CooIncomedisburse\OperUnAudit.cs" />
    <Compile Include="Report\CooIncomedisburse\OperUnSubmit.cs" />
    <Compile Include="Report\CooIncomedisburse\QueryListReportData.cs" />
    <Compile Include="Report\CostPoolMx\AbstractQueryDyn.cs" />
    <Compile Include="Report\CostPoolMx\QueryListReport.cs" />
    <Compile Include="Report\CostPoolMx\QueryListReportData.cs" />
    <Compile Include="Report\CostPoolMx\QuerySelector.cs" />
    <Compile Include="Report\CostPool\QueryListReportData.cs" />
    <Compile Include="Report\CustomerBalanceDetail\QueryListReport.cs" />
    <Compile Include="Report\CustomerBalanceDetail\QueryListReportData.cs" />
    <Compile Include="Report\CustomerBalance\QueryListReportData.cs" />
    <Compile Include="Report\DeliverBalanceList\QueryListReportData.cs" />
    <Compile Include="Report\DeliverBalance\QueryListReport.cs" />
    <Compile Include="Report\DeliverBalance\QueryListReportData.cs" />
    <Compile Include="Report\GlobStockSummary\GlobStockSummary.cs" />
    <Compile Include="Report\GlobStockSummary\UpdateData.cs" />
    <Compile Include="Report\OrderBalance\FilterNew.cs" />
    <Compile Include="Report\OrdertoBuy\New.cs" />
    <Compile Include="Report\OrdertoBuy\PushPurOrder.cs" />
    <Compile Include="Report\OrdertoBuy\PushPurOrderSWJ.cs" />
    <Compile Include="Report\OrdertoBuy\QueryListReport.cs" />
    <Compile Include="Report\OrdertoBuy\QueryListReportData.cs" />
    <Compile Include="Report\IndbQty\QueryListReport.cs" />
    <Compile Include="Report\IndbQty\QueryListReportData.cs" />
    <Compile Include="Report\PurchaseStockQty\QueryListReport.cs" />
    <Compile Include="Report\PurchaseStockQty\QueryListReportData.cs" />
    <Compile Include="Report\OutingAmount\QueryListReport.cs" />
    <Compile Include="Report\OutingAmount\QueryListReportData.cs" />
    <Compile Include="Report\ProductContrast\QueryListReportData.cs" />
    <Compile Include="Report\ProvinceStockSummary\gettopcategory.cs" />
    <Compile Include="Report\ProvinceStockSummary\QueryListReport.cs" />
    <Compile Include="Report\ProvinceStockSummary\QueryListReportData.cs" />
    <Compile Include="Report\PurchasePlan\BuyNow.cs" />
    <Compile Include="Report\RegionStockSummary\gettopcategory.cs" />
    <Compile Include="Report\RegionStockSummary\QueryListReport.cs" />
    <Compile Include="Report\RegionStockSummary\QueryListReportData.cs" />
    <Compile Include="Report\ResidentialBuild\QueryListReportData.cs" />
    <Compile Include="Report\SalesOrder\QueryListReportData.cs" />
    <Compile Include="Report\SelfBuildProduct\QueryListReportData.cs" />
    <Compile Include="Report\StockAgeAnalysis\StockAgeAnalysisService.cs" />
    <Compile Include="Report\StockAgeAnalysis\UpdateStockAgeData.cs" />
    <Compile Include="Report\StockAgeDetail\QueryListReport.cs" />
    <Compile Include="Report\StockAgeDetail\QueryListReportData.cs" />
    <Compile Include="Report\StockOutStoreSlaer\QueryListReport.cs" />
    <Compile Include="Report\StockOutStoreSlaer\QueryListReportData.cs" />
    <Compile Include="Report\AgentStockSummary\QueryListReportData.cs" />
    <Compile Include="Report\StoreSales\QueryListReportData.cs" />
    <Compile Include="Report\TempCustomer\QueryListReportData.cs" />
    <Compile Include="Report\UserFieldsPermisseExecute\QueryListReportData.cs" />
    <Compile Include="Report\UserDataPermissionReport\QueryListReportData.cs" />
    <Compile Include="SchedulerTask\GetOpeningBankData.cs" />
    <Compile Include="SchedulerTask\StockWaterlevelAutoCalculate.cs" />
    <Compile Include="SchedulerTask\ClearSecondProductAuthTask.cs" />
    <Compile Include="SchedulerTask\DeliverAutoAudit.cs" />
    <Compile Include="SchedulerTask\DistPurcchasePrice.cs" />
    <Compile Include="SchedulerTask\PieceSend\AutoAuditPoStockIn.cs" />
    <Compile Include="SchedulerTask\PieceSend\AutoCreateSoStockOut.cs" />
    <Compile Include="SchedulerTask\PieceSend\E3Retry.cs" />
    <Compile Include="SchedulerTask\PieceSend\GetE3LogisticsProgress.cs" />
    <Compile Include="SchedulerTask\PieceSend\UpdateOrderLogisticsProgress.cs" />
    <Compile Include="SchedulerTask\BarCodeMasterAutoArchiving.cs" />
    <Compile Include="SchedulerTask\ProductmonthlySalesTask.cs" />
    <Compile Include="SchedulerTask\ReserveAutoArchiving.cs" />
    <Compile Include="SchedulerTask\RebuildProductDataIsolateTempTblTask.cs" />
    <Compile Include="SchedulerTask\InitProductDataIsolateTempTblTask.cs" />
    <Compile Include="SchedulerTask\StoreAutoAudit.cs" />
    <Compile Include="SchedulerTask\AgentAutoAudit.cs" />
    <Compile Include="SchedulerTask\PurOrderChgAutoAudit.cs" />
    <Compile Include="SchedulerTask\ServiceAutoClose.cs" />
    <Compile Include="SchedulerTask\PriceCalculate.cs" />
    <Compile Include="SchedulerTask\StockAgeAnalysis.cs" />
    <Compile Include="SchedulerTask\StoreCreateDeptTask.cs" />
    <Compile Include="SchedulerTask\SyncProductVolume.cs" />
    <Compile Include="SchedulerTask\ZY\AutoAuditZYPoStockIn.cs" />
    <Compile Include="SchedulerTask\ZY\AutoCreateZYSoStockOut.cs" />
    <Compile Include="SchedulerTask\ZY\ZYGetE3LogisticsProgress.cs" />
    <Compile Include="ServiceAutoCloseService.cs" />
    <Compile Include="ServiceInst\FinanceClosedAccountsService.cs" />
    <Compile Include="Service\AgentService_Initialize.cs" />
    <Compile Include="Service\PurchaseOrderStockWaterlineService.cs" />
    <Compile Include="Service\StockWaterLevelService.cs" />
    <Compile Include="Service\Archiving\ArchivingService.cs" />
    <Compile Include="Service\CrmDistributorService.cs" />
    <Compile Include="Service\DeliverService.cs" />
    <Compile Include="MuSi\Service\RepairAuxPropValue.cs" />
    <Compile Include="MuSi\Interface\SystemIntegration\IMuSiBizObjMapService.cs" />
    <Compile Include="MuSi\Interface\SystemIntegration\IMuSiFormServiceLoader.cs" />
    <Compile Include="PDA\Inventoryverify\WareHouseScan.cs" />
    <Compile Include="PDA\Inventoryverify\InitialLoad.cs" />
    <Compile Include="PDA\Inventoryverify\ScanSubmit.cs" />
    <Compile Include="PDA\WmsCommon\GetWMSLocation.cs" />
    <Compile Include="PDA\WmsCommon\GetWMSStock.cs" />
    <Compile Include="Plugin\Auth\PermissionEvent.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_Admin.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_Stock.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_Fin.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_Sales.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_Shopkeeper.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_Flow.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo_Boss.cs" />
    <Compile Include="Plugin\Auth\StaffDataRule.cs" />
    <Compile Include="Plugin\Auth\SupplierDataRule.cs" />
    <Compile Include="Plugin\BAS\Agent\Delete.cs" />
    <Compile Include="Plugin\BAS\Agent\Forbid.cs" />
    <Compile Include="Plugin\BAS\Agent\Save.cs" />
    <Compile Include="Plugin\BAS\Agent\UnForbid.cs" />
    <Compile Include="Plugin\BAS\Deliver\AuditValidation.cs" />
    <Compile Include="Plugin\BAS\Deliver\Save.cs" />
    <Compile Include="Plugin\BAS\MainAgentConfig\Save.cs" />
    <Compile Include="Plugin\BAS\Organization\Forbid.cs" />
    <Compile Include="Plugin\BAS\Organization\UnForbid.cs" />
    <Compile Include="Plugin\BAS\Price\SaveValidation.cs" />
    <Compile Include="Plugin\BAS\SalesControl\Forbid.cs" />
    <Compile Include="Plugin\BAS\SalesControl\UnForbid.cs" />
    <Compile Include="Plugin\BAS\SalesControl\Delete.cs" />
    <Compile Include="Plugin\BAS\Series\Save.cs" />
    <Compile Include="Plugin\BAS\Store\AuditValidation.cs" />
    <Compile Include="Plugin\BCM\ScanResult\LoadScanResult.cs" />
    <Compile Include="Plugin\Helpers\UserInformationHelper.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\UnbindContract.cs" />
    <Compile Include="Plugin\Pur\ReqOrder\Push.cs" />
    <Compile Include="Plugin\Sec\UserBizRules.cs" />
    <Compile Include="Plugin\Sec\ForbidUser.cs" />
    <Compile Include="Plugin\Sec\SaveSecUser.cs" />
    <Compile Include="Plugin\STE\Customer\Validation_Phone.cs" />
    <Compile Include="Plugin\STK\PoStockIn\CheckLinkReserveInfo.cs" />
    <Compile Include="Plugin\STK\PoStockIn\Cancel.cs" />
    <Compile Include="Plugin\STK\PoStockReturn\Audit.cs" />
    <Compile Include="Plugin\STK\PoStockReturn\UnAudit.cs" />
    <Compile Include="Plugin\STK\SoStockOut\UnCancel.cs" />
    <Compile Include="Plugin\STK\SoStockOut\Cancel.cs" />
    <Compile Include="Plugin\StockCtrlSave.cs" />
    <Compile Include="Plugin\FIN\HeadStatement\Cancel.cs" />
    <Compile Include="Plugin\FIN\HeadStatement\Save.cs" />
    <Compile Include="Plugin\FIN\HeadStatement\UndoBill.cs" />
    <Compile Include="Plugin\FIN\HeadStatement\ConfirmBill.cs" />
    <Compile Include="Plugin\FIN\HeadStatement\UnconfirmBill.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\UnAudit.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\Audit.cs" />
    <Compile Include="Plugin\IM\Notice\Renow.cs" />
    <Compile Include="Plugin\IM\Notice\Down.cs" />
    <Compile Include="Plugin\IM\Notice\Up.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\OrderClose.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SearchdDeliverids.cs" />
    <Compile Include="Plugin\Sel\Category\UpdateAuxPropValueSet.cs" />
    <Compile Include="Plugin\Sel\PropSelectionSingle\LoadRelationProp.cs" />
    <Compile Include="Plugin\Sel\PropValue\PropValueHelper.cs" />
    <Compile Include="Plugin\SER\Category\Save.cs" />
    <Compile Include="Plugin\SER\Category\LoadParentCategory.cs" />
    <Compile Include="Plugin\STE\Channel\CheckMustCfg.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\GetAreaByDept.cs" />
    <Compile Include="Plugin\STE\Customer\GetAreaByMyDept.cs" />
    <Compile Include="Plugin\STE\Order\GetDeptByStaff.cs" />
    <Compile Include="Plugin\STE\Order\Validation_Inv.cs" />
    <Compile Include="Plugin\STE\Order\CheckIsNewChannel.cs" />
    <Compile Include="Plugin\STE\Order\CheckIsResultBrand.cs" />
    <Compile Include="Plugin\STE\Order\Common\OrderASHX.cs" />
    <Compile Include="Plugin\STE\Order\Common\ResultFmt.cs" />
    <Compile Include="Plugin\STE\Order\StandardCustomBatch.cs" />
    <Compile Include="Plugin\STE\Order\EntryBizClose.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Save.UpdatePrdAuth.cs" />
    <Compile Include="Plugin\STE\Order\UnChange.cs" />
    <Compile Include="Plugin\STE\ReserveUtil.cs" />
    <Compile Include="Plugin\STE\Stockoutreport\QueryListReportData.cs" />
    <Compile Include="Plugin\STK\InventoryBase\UnAuditCheck.cs" />
    <Compile Include="Plugin\STK\PoStockIn\Pull.cs" />
    <Compile Include="Plugin\STK\PoStockIn\QuerySelector.cs" />
    <Compile Include="Plugin\STK\SoStockOut\LinkFormSearch.cs" />
    <Compile Include="Plugin\STK\SoStockOut\New.cs" />
    <Compile Include="Plugin\STK\SoStockOut\Pull.cs" />
    <Compile Include="Plugin\STK\SoStockOut\QuerySelector.cs" />
    <Compile Include="Plugin\STK\SoStockOut\Submit.cs" />
    <Compile Include="Report\OrderBalance\QueryListReportData.cs" />
    <Compile Include="Report\PurchasePlan\QueryListReportData.cs" />
    <Compile Include="Report\PurchasePrice\AbstractQueryDyn.cs" />
    <Compile Include="Report\PurchasePrice\FuzzyQuery.cs" />
    <Compile Include="Report\PurchasePrice\PurchasePriceChart.cs" />
    <Compile Include="Report\PurchasePrice\QuerySelector.cs" />
    <Compile Include="ResultBrandService.cs" />
    <Compile Include="MuSi\FormService\MuSiFormServiceLoader.cs" />
    <Compile Include="MuSi\DTO\HSUnconfirmBillDTO.cs" />
    <Compile Include="MuSi\DTO\HSConfirmBillDTO.cs" />
    <Compile Include="MuSi\DTO\UnstdAuditDTO.cs" />
    <Compile Include="MuSi\FormService\SystemIntegration\AfterPackSourceBillEventArgs.cs" />
    <Compile Include="MuSi\FormService\SystemIntegration\AfterSendEventArgs.cs" />
    <Compile Include="MuSi\FormService\SystemIntegration\BeforeFieldMappingEventArgs.cs" />
    <Compile Include="MuSi\FormService\SystemIntegration\BeforePackSourceBillEventArgs.cs" />
    <Compile Include="MuSi\FormService\SystemIntegration\BeforeSendEventArgs.cs" />
    <Compile Include="MuSi\FormService\SystemIntegration\ParseResultEventArgs.cs" />
    <Compile Include="MuSi\Interface\SystemIntegration\AbstractSyncDataToMuSiPlugIn.cs" />
    <Compile Include="MuSi\Interface\IMuSiService.cs" />
    <Compile Include="MuSi\Interface\SystemIntegration\ISyncDataToMuSiPlugIn.cs" />
    <Compile Include="MuSi\MuSiApi.cs" />
    <Compile Include="MuSi\Response\MuSiResponse.cs" />
    <Compile Include="MuSi\Plugin\BaseMSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\BAS\Product\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrder\BizClose.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrder\SubmitHQ.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrder\TransferToMuSi.cs" />
    <Compile Include="MuSi\Plugin\SEL\Range\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\SEL\Category\MSSaveSync.cs" />
    <Compile Include="MuSi\Plugin\SI\MuSiBizObjMap\QueryField.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\AbstractQueryDyn.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\CheckOrderResultBrand.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\CheckTransferOrderApproving.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\FuzzyQuery.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\GetSeriesData.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\QuerySelectorDyn.cs" />
    <Compile Include="MuSi\Service\MuSiService.cs" />
    <Compile Include="MuSi\OperationService\MSCallback.cs" />
    <Compile Include="MuSi\OperationService\MSSaveSync.cs" />
    <Compile Include="MuSi\OperationService\SyncToMusi.cs" />
    <Compile Include="Plugin\AFT\Repairorder\Audit.cs" />
    <Compile Include="Plugin\AFT\Repairorder\PushOtherOutOrStorage.cs" />
    <Compile Include="Plugin\AFT\Repairorder\PushOtherReceivablesOrPayables.cs" />
    <Compile Include="Plugin\AFT\Repairorder\Save.cs" />
    <Compile Include="Plugin\Auth\DeliveryDataRule.cs" />
    <Compile Include="Plugin\Auth\PreRolePermInfo\PreRolePermInfo.cs" />
    <Compile Include="Plugin\Auth\PrintTmplDataRule.cs" />
    <Compile Include="Plugin\Auth\ApolegamicDataRule.cs" />
    <Compile Include="Plugin\Auth\BizModuleView.cs" />
    <Compile Include="Plugin\Auth\AllAgenTopOrgDataRules.cs" />
    <Compile Include="Plugin\Auth\DataAuthHelp.cs" />
    <Compile Include="Plugin\Auth\OrgDataRule.cs" />
    <Compile Include="Plugin\Auth\ProductDataHelp.cs" />
    <Compile Include="Plugin\Auth\ProductDataRule.cs" />
    <Compile Include="Plugin\Auth\DeptDataRule.cs" />
    <Compile Include="Plugin\Auth\StockDataRule.cs" />
    <Compile Include="Plugin\Auth\StoreDataRule.cs" />
    <Compile Include="Plugin\Auth\RoleDataRule.cs" />
    <Compile Include="Plugin\Auth\PriceDataRule.cs" />
    <Compile Include="Plugin\Auth\BrandSeriDataRule.cs" />
    <Compile Include="Plugin\Auth\UserDataRule.cs" />
    <Compile Include="Plugin\Auth\UnitDataRule.cs" />
    <Compile Include="Plugin\BAS\Agent\Audit.cs" />
    <Compile Include="Plugin\BAS\Appletparameter\Save.cs" />
    <Compile Include="Plugin\BAS\Deliver\Audit.cs" />
    <Compile Include="Plugin\BAS\MainAuxProp\Copy.cs" />
    <Compile Include="Plugin\BAS\MainAuxProp\Modify.cs" />
    <Compile Include="Plugin\BAS\MainAuxProp\New.cs" />
    <Compile Include="MuSi\Plugin\BAS\Price\MSSaveSync.cs" />
    <Compile Include="Plugin\BAS\OrgResultBrand\Save.cs" />
    <Compile Include="Plugin\BAS\Price\Audit.cs" />
    <Compile Include="Plugin\BAS\Price\Push.cs" />
    <Compile Include="Plugin\BAS\Price\Modify.cs" />
    <Compile Include="Plugin\BAS\Price\AdjustProductPrice.cs" />
    <Compile Include="Plugin\BAS\Price\Submit.cs" />
    <Compile Include="Plugin\BAS\Price\UnAudit.cs" />
    <Compile Include="Plugin\BAS\Price\UnSubmit.cs" />
    <Compile Include="Plugin\BAS\Price\ValidationOrgOperation.cs" />
    <Compile Include="Plugin\BAS\ProductAuth\Save.cs" />
    <Compile Include="Plugin\BAS\Product\UnAudit.cs" />
    <Compile Include="Plugin\BAS\Product\UnForbid.cs" />
    <Compile Include="Plugin\BAS\Product\Forbid.cs" />
    <Compile Include="Plugin\BAS\Product\UnSubmit.cs" />
    <Compile Include="Plugin\BAS\Product\Audit.cs" />
    <Compile Include="Plugin\BAS\Product\Submit.cs" />
    <Compile Include="Plugin\BAS\Product\ValidationOrgOperation.cs" />
    <Compile Include="Plugin\BAS\Product\UpdatePrice.cs" />
    <Compile Include="MuSi\Plugin\BAS\Unit\MSSaveSync.cs" />
    <Compile Include="Plugin\BillConvert\Order2TransferOrderApplyConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\SalOrder2OutStockShipperConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\TransferOrderApply2PayReceipt.cs" />
    <Compile Include="MuSi\Plugin\PUR\PurchaseOrder\UnstdTypeAudit.cs" />
    <Compile Include="Plugin\Helpers\AuditOrUnAuditHelper.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\DeleteRow.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\GetSupplierByDeliverId.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\GetProductByOrgid.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\PurCommon.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Submit.cs" />
    <Compile Include="Plugin\Sal\Collectreceipt\Audit.cs" />
    <Compile Include="Plugin\Sal\Collectreceipt\UnAudit.cs" />
    <Compile Include="Plugin\Sal\TransferOrderApply\Push2TransferOrderConvertPlugIn.cs" />
    <Compile Include="Plugin\Sal\TransferOrderApply\Settlement.cs" />
    <Compile Include="Plugin\Sec\LevelAdminAccessSave.cs" />
    <Compile Include="Plugin\Sel\Category\Save.cs" />
    <Compile Include="Plugin\Sel\Constraint\Save.cs" />
    <Compile Include="Plugin\Sel\FittingsMap\Save.cs" />
    <Compile Include="Plugin\Sel\PriceFormula\Save.cs" />
    <Compile Include="Plugin\Sel\PropSelectionSingle\AbstractQueryDyn.cs" />
    <Compile Include="Plugin\Sel\PropSelectionSingle\CheckPropValue.cs" />
    <Compile Include="Plugin\Sel\PropSelectionSingle\QuerySelectorDyn.cs" />
    <Compile Include="Plugin\Sel\PropSelectionSingle\FuzzyQueryDyn.cs" />
    <Compile Include="Plugin\Sel\PropSelectionSingle\New.cs" />
    <Compile Include="Plugin\Sel\PropValue\LoadOrCreate.cs" />
    <Compile Include="Plugin\Sel\PropValue\LoadPropValueImage.cs" />
    <Compile Include="Plugin\BAS\Store\Audit.cs" />
    <Compile Include="Plugin\Sel\PropValue\QueryData.cs" />
    <Compile Include="Plugin\Sel\PropValue\Save.cs" />
    <Compile Include="Plugin\Sec\LevelAdminAccess.cs" />
    <Compile Include="Plugin\Sec\RoleListTree.cs" />
    <Compile Include="Plugin\Sec\UserListTree.cs" />
    <Compile Include="Plugin\Sel\Category\LoadPropEntrys.cs" />
    <Compile Include="MuSi\Plugin\SEL\Prop\MSSaveSync.cs" />
    <Compile Include="Plugin\Sel\Prop\Save.cs" />
    <Compile Include="MuSi\Plugin\SEL\SuiteMap\MSSaveSync.cs" />
    <Compile Include="Plugin\Sel\Range\Save.cs" />
    <Compile Include="Plugin\Sel\Selection\FuzzyQueryDyn.cs" />
    <Compile Include="Plugin\Sel\Selection\QuerySelectorDyn.cs" />
    <Compile Include="Plugin\Sel\Selection\ShowSuiteSelection.cs" />
    <Compile Include="Plugin\STE\Order\BatchModify.cs" />
    <Compile Include="Plugin\STE\Order\CheckSelsuit.cs" />
    <Compile Include="Plugin\STE\Order\CheckCategory.cs" />
    <Compile Include="Plugin\STE\Order\Common\PurchaseOrderCommon.cs" />
    <Compile Include="Plugin\STE\Order\Common\OrderCommon.cs" />
    <Compile Include="Plugin\STE\Order\DeleteRow.cs" />
    <Compile Include="Plugin\STE\Order\DoAddParts_Custom.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\TransferToMuSi.cs" />
    <Compile Include="Plugin\STE\Order\DoAddParts.cs" />
    <Compile Include="Plugin\STE\Order\IsNoOrders.cs" />
    <Compile Include="Plugin\STE\Order\IsPurchaseinvoice.cs" />
    <Compile Include="Plugin\STE\Order\PushTransferOrderApplyBatch.cs" />
    <Compile Include="Plugin\STE\Order\PushTransferOrderApply.cs" />
    <Compile Include="Plugin\STE\Order\SyncChange.cs" />
    <Compile Include="Plugin\STE\Order\UnCancel.cs" />
    <Compile Include="MuSi\Plugin\STE\Order\UnstdTypeAudit.cs" />
    <Compile Include="Plugin\STE\Order\UpdateSyn.cs" />
    <Compile Include="Plugin\STE\SaleIntention\UnCancel.cs" />
    <Compile Include="Plugin\STK\InventoryList\ClearSuiteStock.cs" />
    <Compile Include="Plugin\STK\ScheduleApply\Delete.cs" />
    <Compile Include="Plugin\STK\SoStockOut\Save.cs" />
    <Compile Include="Plugin\STK\StockParam\ViewStockParam.cs" />
    <Compile Include="Plugin\STK\StoreHouse\DefaultStock.cs" />
    <Compile Include="Report\EemployeeSalesrevenue\QueryListReportData.cs" />
    <Compile Include="Helper\DirectSynergyHelper.cs" />
    <Compile Include="Plugin\BAS\Case\Copy.cs" />
    <Compile Include="Plugin\BAS\Case\Undercarriage.cs" />
    <Compile Include="Plugin\BAS\MainAuxProp\CheckBillHeadUnique.cs" />
    <Compile Include="Plugin\BAS\MainAuxProp\Delete.cs" />
    <Compile Include="Plugin\BAS\MainAuxProp\DeleteRow.cs" />
    <Compile Include="Plugin\BAS\MainAuxProp\MainAuxPropHelper.cs" />
    <Compile Include="Plugin\BAS\MainAuxProp\Save.cs" />
    <Compile Include="Plugin\BAS\ProductBarCode\PrintTmpl.cs" />
    <Compile Include="Plugin\Pur\Purchaseinvoice\Save.cs" />
    <Compile Include="Plugin\STE\CustomerLevel\GetAll.cs" />
    <Compile Include="Plugin\STE\CustomerLevel\SyncSave.cs" />
    <Compile Include="Plugin\STE\Customer\CheckCustomerByPhone.cs" />
    <Compile Include="Plugin\STE\Order\ManageAttachment.cs" />
    <Compile Include="CustomerBalanceService.cs" />
    <Compile Include="Plugin\BD\AuxPropValueMap\Save.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\CusReturn.cs" />
    <Compile Include="Plugin\STE\Customer\Recycle.cs" />
    <Compile Include="CustomerRecordService.cs" />
    <Compile Include="CustomerService.cs" />
    <Compile Include="CustomeUserEvent.cs" />
    <Compile Include="HtmlFormExtentions.cs" />
    <Compile Include="IM\BizMessageHub.cs" />
    <Compile Include="IM\BizMessageServiceProxy.cs" />
    <Compile Include="IM\IJsClientProxy.cs" />
    <Compile Include="IncomeDisburseService.cs" />
    <Compile Include="MasterService.cs" />
    <Compile Include="OperationService\SendQywxMessage.cs" />
    <Compile Include="OperationService\ApprovalDetail.cs" />
    <Compile Include="OperationService\SavePrintFiles.cs" />
    <Compile Include="OperationService\GetMenuMeta.cs" />
    <Compile Include="OperationService\GetFileDetail.cs" />
    <Compile Include="OperationService\GetSynFileDetail.cs" />
    <Compile Include="OperationService\NewSettle.cs" />
    <Compile Include="OperationService\ProductTagPrintSeting.cs" />
    <Compile Include="OutSpotAutoReserveService.cs" />
    <Compile Include="Plugin\AbdesignerProposal.cs" />
    <Compile Include="Plugin\AFT\Manage\Distributes.cs" />
    <Compile Include="Plugin\AFT\Vist\Save.cs" />
    <Compile Include="Plugin\BAS\Auxproperty\Delete.cs" />
    <Compile Include="Plugin\BAS\Auxproperty\DeleteAuxPropValue.cs" />
    <Compile Include="Plugin\BAS\Auxproperty\GetAuxProperties.cs" />
    <Compile Include="Plugin\BAS\Auxproperty\GetAuxPropValues.cs" />
    <Compile Include="Plugin\BAS\Auxproperty\SearchAuxProp.cs" />
    <Compile Include="Plugin\BAS\Auxproperty\SyncDownloadConfirm.cs" />
    <Compile Include="Plugin\BAS\BankNum\QueryAll.cs" />
    <Compile Include="Plugin\BAS\BankNum\Save.cs" />
    <Compile Include="Plugin\BAS\Case\GetList.cs" />
    <Compile Include="Plugin\BAS\Case\Save.cs" />
    <Compile Include="Plugin\BAS\Category\QueryListTree.cs" />
    <Compile Include="Plugin\BAS\Category\Save.cs" />
    <Compile Include="Plugin\BAS\Category\SyncDownloadConfirm.cs" />
    <Compile Include="Plugin\BAS\CommodityGallery\GetImage.cs" />
    <Compile Include="Plugin\BAS\Dept\DeptQueryListTree.cs" />
    <Compile Include="Plugin\BAS\Dept\DeptSave.cs" />
    <Compile Include="Plugin\BAS\Dept\SyncDownloadConfirm.cs" />
    <Compile Include="Plugin\BAS\Dept\TreeSyncDownloadConfirm.cs" />
    <Compile Include="Plugin\BAS\Enum\Distribute.cs" />
    <Compile Include="Plugin\BAS\Enum\SyncDownloadConfirm.cs" />
    <Compile Include="Plugin\BAS\Enum\SyncEnum.cs" />
    <Compile Include="Plugin\BAS\Example\Save.cs" />
    <Compile Include="Plugin\BAS\MaterialInit\CreateModel.cs" />
    <Compile Include="Plugin\BAS\MaterialInit\ImportError.cs" />
    <Compile Include="Plugin\BAS\MaterialInit\ImportResult.cs" />
    <Compile Include="Plugin\BAS\MaterialInit\MaterialIn.cs" />
    <Compile Include="Plugin\BAS\MaterialInit\MaterialInitTaskService.cs" />
    <Compile Include="Plugin\BAS\Price\BaseDelete.cs" />
    <Compile Include="Plugin\BAS\Price\CancelConfirm.cs" />
    <Compile Include="Plugin\BAS\Price\ClearPriceCache.cs" />
    <Compile Include="Plugin\BAS\Price\Confirm.cs" />
    <Compile Include="Plugin\BAS\Price\Delete.cs" />
    <Compile Include="Plugin\BAS\Price\DeleteRow.cs" />
    <Compile Include="Plugin\BAS\Price\Forbid.cs" />
    <Compile Include="Plugin\BAS\Price\GetPrices.cs" />
    <Compile Include="Plugin\BAS\Price\GetProductPrice.cs" />
    <Compile Include="Plugin\BAS\Price\PriceAdjustService.cs" />
    <Compile Include="Plugin\BAS\Price\Save.cs" />
    <Compile Include="Plugin\BAS\Price\SetConfirmStatus.cs" />
    <Compile Include="Plugin\BAS\Price\UnForbid.cs" />
    <Compile Include="Plugin\BAS\ProductBarCode\GetBarCodeValue.cs" />
    <Compile Include="Plugin\BAS\ProductBarCode\GetProductInfo.cs" />
    <Compile Include="Plugin\BAS\ProductTagPrintSeting\ProductTagPrint.cs" />
    <Compile Include="Plugin\BAS\Product\Copy.cs" />
    <Compile Include="Plugin\BAS\Product\Delete.cs" />
    <Compile Include="Plugin\BAS\Product\Distribute.cs" />
    <Compile Include="Plugin\BAS\Product\GetAuxPropValueSet.cs" />
    <Compile Include="Plugin\BAS\Product\GetDeliveryMode.cs" />
    <Compile Include="Plugin\BAS\Product\GetDetail.cs" />
    <Compile Include="Plugin\BAS\Product\GetImages.cs" />
    <Compile Include="Plugin\BAS\Product\GetList.cs" />
    <Compile Include="Plugin\BAS\Product\GetPlatformProduct.cs" />
    <Compile Include="Plugin\BAS\Product\GetRelevantData.cs" />
    <Compile Include="Plugin\BAS\Product\LoadProduct.cs" />
    <Compile Include="Plugin\BAS\Product\MatchPrice.cs" />
    <Compile Include="Plugin\BAS\Product\MatchProduct.cs" />
    <Compile Include="Helper\ProductUnitHelper.cs" />
    <Compile Include="Plugin\BAS\Product\QueryListTree.cs" />
    <Compile Include="Plugin\BAS\Product\Save.cs" />
    <Compile Include="Plugin\BAS\Product\SetProductOff.cs" />
    <Compile Include="Plugin\BAS\Product\SetProductPublish.cs" />
    <Compile Include="Plugin\BAS\Product\SupplierSyncDownloadConfirm.cs" />
    <Compile Include="Plugin\BAS\Product\SyncDownloadConfirm.cs" />
    <Compile Include="Plugin\BAS\Product\SynProduct.cs" />
    <Compile Include="Plugin\BAS\Product\SynToNewProduct.cs" />
    <Compile Include="Plugin\BAS\Product\SynToProduct.cs" />
    <Compile Include="Plugin\BAS\PushShareData\SynPushShareData.cs" />
    <Compile Include="Plugin\BAS\SalesControl\Save.cs" />
    <Compile Include="Plugin\BAS\Series\SyncDownloadConfirm.cs" />
    <Compile Include="Plugin\BAS\Staff\CreateUserLink.cs" />
    <Compile Include="Plugin\BAS\Staff\SaveRolePermission.cs" />
    <Compile Include="Plugin\BCM\PackOrder\IsExistPackOrder.cs" />
    <Compile Include="Plugin\BCM\PackOrder\Save.cs" />
    <Compile Include="Plugin\BCM\PackOrder\UnAudit.cs" />
    <Compile Include="Plugin\BCM\ReceptionScanTask\CommitInStock.cs" />
    <Compile Include="Plugin\BCM\ReceptionScanTask\CreatePackage.cs" />
    <Compile Include="Plugin\BCM\Service\PackOrderService.cs" />
    <Compile Include="Plugin\BCM\Validation\InStockUnAuditForBarcodeValidation.cs" />
    <Compile Include="Plugin\BillConvert\BaseScanTaskConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\InventoryTransferReq2InventoryTransferConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\Leads2CustomerrecordConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\Order2InventoryTransferAutoConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\Order2InventoryTransferConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\Order2PurchaseOrderConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\Order2ReqOrderConvertPlugin.cs" />
    <Compile Include="Plugin\BillConvert\Order2ServiceConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\OtherStockInReq2OtherStockInConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\OtherStockOutReq2OtherStockOutConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\PurReceiptNotice2PoStockInConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\ReturnNotice2PoStockReturnConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\ReturnNotice2SoStockReturnConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\SaleIntention2OrderConvertPlugIn.cs" />
    <Compile Include="Plugin\CommonSave.cs" />
    <Compile Include="Plugin\Dashboard\MyTask\GetMyTaskDetail.cs" />
    <Compile Include="Plugin\FIN\ChargeDialog\SynergyAccountData.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\PublishSynergy.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\SyncFsWidgetData.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\UnConfirm.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\UnVerific.cs" />
    <Compile Include="Plugin\FIN\Registfee\Audit.cs" />
    <Compile Include="Plugin\FIN\Registfee\Delete.cs" />
    <Compile Include="Plugin\FIN\Registfee\LoadSettleInfo.cs" />
    <Compile Include="Plugin\FIN\Registfee\NewSettle.cs" />
    <Compile Include="Plugin\FIN\Registfee\Save.cs" />
    <Compile Include="Plugin\FIN\Registfee\UnAudit.cs" />
    <Compile Include="Plugin\MP\RoleMenu\GetUserPermission.cs" />
    <Compile Include="Plugin\MP\AssignRight\GetRolePermission.cs" />
    <Compile Include="Plugin\MP\AssignRight\GetPermRole.cs" />
    <Compile Include="Plugin\MP\AssignRight\SaveRolePermission.cs" />
    <Compile Include="Plugin\MP\MPTabbarModel.cs" />
    <Compile Include="Plugin\MP\MPMenuHelper.cs" />
    <Compile Include="Plugin\MP\MPPermHelper.cs" />
    <Compile Include="Plugin\MP\MenuEditor\GetMenuList.cs" />
    <Compile Include="Plugin\MP\MenuEditor\SaveMenus.cs" />
    <Compile Include="Plugin\MP\RoleMPMenuPermitInfo.cs" />
    <Compile Include="Plugin\Msg\SynMsg\SynMsgHandlerKeyConst.cs" />
    <Compile Include="Plugin\Msg\SynMsg\Syn_PushShareDataMsgProc.cs" />
    <Compile Include="Plugin\MT\PartsSelection\Save.cs" />
    <Compile Include="Plugin\MT\SelectionBase.cs" />
    <Compile Include="Plugin\MT\SelectionDimension\Audit.cs" />
    <Compile Include="Plugin\MT\SelectionDimension\ProductDimension.cs" />
    <Compile Include="Plugin\MT\SelectionDimension\Save.cs" />
    <Compile Include="Plugin\MT\SelectionForm\SelectionConfirm.cs" />
    <Compile Include="Plugin\MT\SelectionForm\ShowSelection.cs" />
    <Compile Include="Plugin\MT\SelectionRange\GetDimension.cs" />
    <Compile Include="Plugin\MT\SelectionRange\Modify.cs" />
    <Compile Include="Plugin\MT\SelectionRange\New.cs" />
    <Compile Include="Plugin\MT\SelectionRange\Query.cs" />
    <Compile Include="Plugin\MT\SelectionRange\SyncSave.cs" />
    <Compile Include="Plugin\MT\SelectionRule\SelectionRuleEnumdataForm.cs" />
    <Compile Include="Plugin\MT\SelectionRule\SelectionRuleForm.cs" />
    <Compile Include="Plugin\MT\SuiteSelection\GetPartsSelection.cs" />
    <Compile Include="Plugin\MT\SuiteSelection\Save.cs" />
    <Compile Include="Plugin\MT\Suite\Save.cs" />
    <Compile Include="Plugin\MT\UpdateMTScheme.cs" />
    <Compile Include="Plugin\MT\UpdateMTSchemePlugIn.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\AuditSynergy.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Push.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SalChangeRepealSynergy.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SubmitChange.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Change.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Audit.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Cancel.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\CreateSynBizObject.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\LinkFormSearch.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\NewSettle.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SelectionProc.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SubmitAgain.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SubmitBase.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Unaudit.cs" />
    <Compile Include="Plugin\Pur\ReqOrder\QueryInventory.cs" />
    <Compile Include="Plugin\Sal\ShopCart\QueryData.cs" />
    <Compile Include="Plugin\SER\Master\GetMasterData.cs" />
    <Compile Include="Plugin\SER\Master\MasterAuth.cs" />
    <Compile Include="Plugin\SER\Master\MasterCheck.cs" />
    <Compile Include="Plugin\SER\Master\Save.cs" />
    <Compile Include="Plugin\SER\MerchantOrder\Merchantorder.cs" />
    <Compile Include="Plugin\SER\ServiceFeed\GetCount.cs" />
    <Compile Include="Plugin\SER\ServiceFeed\GetData.cs" />
    <Compile Include="Plugin\SER\ServiceFeed\GetDetail.cs" />
    <Compile Include="Plugin\SER\Team\DelMember.cs" />
    <Compile Include="Plugin\SER\Team\GetData.cs" />
    <Compile Include="Plugin\SER\Team\GetDataByTeamId.cs" />
    <Compile Include="Plugin\STE\Channel\AddDuty.cs" />
    <Compile Include="Plugin\STE\Channel\ChangeDuty.cs" />
    <Compile Include="Plugin\STE\Channel\DeleteDuty.cs" />
    <Compile Include="Plugin\STE\CommissionRule\GetDeptBySchemeId.cs" />
    <Compile Include="Plugin\STE\CommissionRule\New.cs" />
    <Compile Include="Plugin\STE\CommissionRule\Save.cs" />
    <Compile Include="Plugin\STE\CommissionScheme\Save.cs" />
    <Compile Include="Plugin\STE\CostAccounting\RefreshEntities.cs" />
    <Compile Include="Plugin\STE\CostAccounting\UnAudit.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\BaseCustomerRecordPlugin.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\Finish.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\GetMenuMeta.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\LinkFormSearch.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\PlaceOrder.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\QueryData.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\QuotedPrice.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\Save.cs" />
    <Compile Include="Plugin\STE\Customer\Audit.cs" />
    <Compile Include="Plugin\STE\Customer\ManageDuty.cs" />
    <Compile Include="Plugin\STE\Customer\Match.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\Validate.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\ValidateDuty.cs" />
    <Compile Include="Plugin\STE\Customer\BaseCustomerPlugin.cs" />
    <Compile Include="Plugin\STE\Customer\LinkFormSearch.cs" />
    <Compile Include="Plugin\STE\Customer\QueryData.cs" />
    <Compile Include="Plugin\STE\Customer\SumMountRefresh.cs" />
    <Compile Include="Plugin\STE\Customer\Validate.cs" />
    <Compile Include="Plugin\STE\FollowerRecord\AppDelete.cs" />
    <Compile Include="Plugin\STE\FollowerRecord\DynamicRemind.cs" />
    <Compile Include="Plugin\STE\FollowerRecord\FollowerRecordService.cs" />
    <Compile Include="Plugin\STE\FollowerRecord\InitBill.cs" />
    <Compile Include="Plugin\STE\FollowerRecord\Save.cs" />
    <Compile Include="Plugin\STE\Goal\New.cs" />
    <Compile Include="Plugin\STE\Leads\GetMenuMeta.cs" />
    <Compile Include="Plugin\STE\Leads\QueryData.cs" />
    <Compile Include="Plugin\STE\LinkProgress\Save.cs" />
    <Compile Include="Plugin\STE\MarketStatement\Audit.cs" />
    <Compile Include="Plugin\STE\MarketStatement\Delete.cs" />
    <Compile Include="Plugin\STE\MarketStatement\Save.cs" />
    <Compile Include="Plugin\STE\MarketStatement\UnAudit.cs" />
    <Compile Include="Plugin\STE\MarketStatement\VerifyIncomeDisburse.cs" />
    <Compile Include="Plugin\STE\OrderChg\QueryData.cs" />
    <Compile Include="Plugin\STE\Order\BrokerageDetail.cs" />
    <Compile Include="Plugin\STE\Order\Cancel.cs" />
    <Compile Include="Plugin\STE\Order\Change.cs" />
    <Compile Include="Plugin\STE\Order\CheckOutspot.cs" />
    <Compile Include="Plugin\STE\Order\GetAutoMatchPrice.cs" />
    <Compile Include="Plugin\STE\Order\InitBill.cs" />
    <Compile Include="Plugin\STE\Order\LinkFormSearch.cs" />
    <Compile Include="Plugin\STE\Order\LoadExDeliveryDate.cs" />
    <Compile Include="Plugin\STE\Order\LockOrder.cs" />
    <Compile Include="Plugin\STE\Order\MergeRegistFee.cs" />
    <Compile Include="Plugin\STE\Order\New.cs" />
    <Compile Include="Plugin\STE\Order\OrderAccept.cs" />
    <Compile Include="Plugin\STE\Order\OrderCancelAccept.cs" />
    <Compile Include="Plugin\STE\Order\OrderClose.cs" />
    <Compile Include="Plugin\STE\Order\OrderUnClose.cs" />
    <Compile Include="Plugin\STE\Order\Push.cs" />
    <Compile Include="Plugin\STE\Order\QueryData.cs" />
    <Compile Include="Plugin\STE\Order\QueryInventory.cs" />
    <Compile Include="Plugin\STE\Order\Refresh.cs" />
    <Compile Include="Plugin\STE\Order\Submit.cs" />
    <Compile Include="Plugin\STE\Order\SubmitBase.cs" />
    <Compile Include="Plugin\STE\Order\SubmitChange.cs" />
    <Compile Include="Plugin\STE\Order\Modify.cs" />
    <Compile Include="Plugin\STE\Order\UnLockOrder.cs" />
    <Compile Include="Plugin\STE\SaleIntention\Cancel.cs" />
    <Compile Include="Plugin\STE\SaleIntention\Chargeback.cs" />
    <Compile Include="Plugin\STE\SaleIntention\InitBill.cs" />
    <Compile Include="Plugin\STE\SaleIntention\LinkFormSearch.cs" />
    <Compile Include="Plugin\LinkFormSearchBase.cs" />
    <Compile Include="Plugin\STE\SaleIntention\Push.cs" />
    <Compile Include="Plugin\STE\SaleIntention\QueryData.cs" />
    <Compile Include="Plugin\STE\SaleIntention\QueryInventory.cs" />
    <Compile Include="Plugin\STE\SaleIntention\SyncSubmitAgain.cs" />
    <Compile Include="Plugin\STE\SaleIntention\UnAudit.cs" />
    <Compile Include="Plugin\STE\StoreStatement\Audit.cs" />
    <Compile Include="Plugin\STE\StoreStatement\Delete.cs" />
    <Compile Include="Plugin\STE\StoreStatement\Save.cs" />
    <Compile Include="Plugin\STE\StoreStatement\UnAudit.cs" />
    <Compile Include="Plugin\STE\StoreStatement\VerifyIncomeDisburse.cs" />
    <Compile Include="Plugin\STK\InventoryBase\Audit.cs" />
    <Compile Include="Plugin\STK\InventoryBase\UnAudit.cs" />
    <Compile Include="Plugin\STK\OtherStockInReq\Push2PackOrder.cs" />
    <Compile Include="Plugin\STK\PoStockIn\Save.cs" />
    <Compile Include="Plugin\STK\PurReceiptNotice\BasePush2PackOrder.cs" />
    <Compile Include="Plugin\STK\PurReceiptNotice\BizClose.cs" />
    <Compile Include="Plugin\STK\PurReceiptNotice\Push2PackOrder.cs" />
    <Compile Include="Plugin\STK\PurReceiptNotice\UnClose.cs" />
    <Compile Include="Plugin\STK\PurReqOrder\Save.cs" />
    <Compile Include="Plugin\STK\Rpt\QueryStockSynthesize.cs" />
    <Compile Include="Plugin\STK\SalReturnNotice\Push2PackOrder.cs" />
    <Compile Include="Plugin\STK\SchedulePlatform\CancelSchedulePlan.cs" />
    <Compile Include="Plugin\STK\StoreHouse\DeleteRow.cs" />
    <Compile Include="Plugin\STK\Supplier\SyncDownloadConfirm.cs" />
    <Compile Include="Plugin\Synergism\syn_updatecompanyname.cs" />
    <Compile Include="Plugin\Sys\AddOrDeleteFile.cs" />
    <Compile Include="Plugin\Sys\GetDetailLine.cs" />
    <Compile Include="Plugin\WeChat\WeiXinWork\PushMsgToWXWork.cs" />
    <Compile Include="Plugin\WeChat\WeiXinWork\SyncUserToWeiXinWork.cs" />
    <Compile Include="ProtecteDataService.cs" />
    <Compile Include="RegisterCompanyEvent.cs" />
    <Compile Include="Report\BaseCapitalChart.cs" />
    <Compile Include="Report\ChannelSaleAbility\QueryListReportData.cs" />
    <Compile Include="Report\HotCakesExecute\QueryListReportData.cs" />
    <Compile Include="Report\OrderDetail\QueryListReportData.cs" />
    <Compile Include="Report\OrderTrackingForm\QueryListReportData.cs" />
    <Compile Include="Report\PurchaseAnalysis\QueryListReportData.cs" />
    <Compile Include="Report\PurchaseDetail\QueryListReportData.cs" />
    <Compile Include="Report\PurchaseRateAnalysis\QueryListReportData.cs" />
    <Compile Include="Report\SalaryCommissionExecute\QueryListReportData.cs" />
    <Compile Include="Report\StoreBuidConversion\QueryListReportData.cs" />
    <Compile Include="Report\StorePerformanceExecute\QueryListReportData.cs" />
    <Compile Include="Report\StoreSalesConversionRate\QueryListReport.cs" />
    <Compile Include="Report\StoreSalesConversionRate\QueryListReportData.cs" />
    <Compile Include="Report\UserBusinesspermisseExecute\QueryListReportData.cs" />
    <Compile Include="OrderService.cs" />
    <Compile Include="MuSi\SchedulerTask\TransferToMuSiTask.cs" />
    <Compile Include="SalesControl.cs" />
    <Compile Include="ServiceInst\LinkProgressService.cs" />
    <Compile Include="MuSi\ServiceInst\MuSiSysSyncService.cs" />
    <Compile Include="ServiceInst\SaveAuxPropertyService.cs" />
    <Compile Include="Plugin\BAS\Schedule\Delete.cs" />
    <Compile Include="Plugin\BAS\Schedule\ScheduleDetail.cs" />
    <Compile Include="Plugin\BAS\Schedule\ScheduleDataByDay.cs" />
    <Compile Include="Plugin\BAS\Schedule\Save.cs" />
    <Compile Include="Plugin\BAS\Staff\StaffSave.cs" />
    <Compile Include="Plugin\BAS\Unit\Save.cs" />
    <Compile Include="Plugin\AbstractAssignDesigner.cs" />
    <Compile Include="Plugin\AbstractAssignScale.cs" />
    <Compile Include="Plugin\BCM\PackOrder\Audit.cs" />
    <Compile Include="Plugin\BCM\PackOrder\CreatePackage.cs" />
    <Compile Include="Plugin\BillConvert\BaseSchedulePlatformConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\PoStockIn2PoReturnNoticeConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\PoStockIn2PoStockReturnConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\PurchaseOrder2PoStockInConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\SalDelivery2OutStockConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\SalOrder2CachBillConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\SalOrder2OutStockConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\SalOrder2SalDeliveryNoticeConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\ScheduleApply2ServiceConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\SoStockOut2ScheduleApplyConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\ScheduleApply2InventoryTransferReqConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\ScheduleApply2OtherStockOutReqConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\ScheduleApply2ReturnNoticeConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\SoStockOut2SoReturnNoticeConvertPlugIn.cs" />
    <Compile Include="Plugin\BillConvert\SoStockOut2SoStockReturnConvertPlugIn.cs" />
    <Compile Include="Plugin\Dashboard\MyTask\QueryMyTask.cs" />
    <Compile Include="Plugin\Dashboard\MyTask\QueryMsgs.cs" />
    <Compile Include="Plugin\Dashboard\MyTask\SetMsgStatus.cs" />
    <Compile Include="Plugin\FIN\ChargeDialog\Charge.cs" />
    <Compile Include="Plugin\FIN\ChargeDialog\GetSynBankNum.cs" />
    <Compile Include="Plugin\FIN\CooSettle\ChargeService.cs" />
    <Compile Include="Plugin\FIN\CooSettle\InpourService.cs" />
    <Compile Include="Plugin\FIN\CooSettle\SyncAccountTransaction.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\Confirm.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\ConfirmSynergy.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\Delete.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\DeleteSynergy.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\Invalid.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\InvalidSynergy.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\LoadData.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\Save.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\SaveSynergy.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\SynAccountBalanceService.cs" />
    <Compile Include="Plugin\FIN\IncomeDisburse\Verific.cs" />
    <Compile Include="Plugin\FIN\InpourDialog\Recharge.cs" />
    <Compile Include="Plugin\FIN\PayAccount\AccountInfoService.cs" />
    <Compile Include="Plugin\FIN\SettleCenter\QueryAssetDetail.cs" />
    <Compile Include="Plugin\FIN\SettleCenter\QueryBalance.cs" />
    <Compile Include="Plugin\MobileShop\AddTimes.cs" />
    <Compile Include="Plugin\MobileShop\GetAllDepartments.cs" />
    <Compile Include="Plugin\MobileShop\GetAllMenus.cs" />
    <Compile Include="Plugin\MobileShop\GetAllStaff.cs" />
    <Compile Include="Plugin\MobileShop\GetDefaultMenus.cs" />
    <Compile Include="Plugin\MobileShop\ListMenus.cs" />
    <Compile Include="Plugin\MobileShop\SaveMenus.cs" />
    <Compile Include="Plugin\MobileShop\SaveMyMenus.cs" />
    <Compile Include="Plugin\AbstractLookScale.cs" />
    <Compile Include="Plugin\Pur\PoReturnDialog\New.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\CancelConfirm.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\CancelReceiv.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\ConfirmReceiv.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Copy.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Delete.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\DynamicRuleImpl.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\InStock.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\LoadData.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\LoadSettleInfo.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\New.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\OrderToPurchaseOrderConvertService.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\PriceFirm.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\RepealSynergy.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\ReturnGood.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SalRepealSynergy.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\Save.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\StockOutNotify.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SubmitSynergy.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SubmitSynergy_Reply.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\SyncFSWidgetData.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\UpdateBizStatus.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\UpdateBizTip.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\UpdateSyn.cs" />
    <Compile Include="Plugin\Pur\PurchaseOrder\UpdateSynBizStatus.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\CancelConfirm.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\Confirm.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\Delete.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\DeleteRow.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\Forbid.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\GetPrice.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\PurchasePriceAdjust.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\Save.cs" />
    <Compile Include="Plugin\Pur\PurchasePrice\UnForbid.cs" />
    <Compile Include="Plugin\Pur\PurchaseSettleDyn\GetAccountBalance.cs" />
    <Compile Include="Plugin\Pur\PurchaseSettleDyn\Settle.cs" />
    <Compile Include="Plugin\Sal\AccountTransfer\Confirm.cs" />
    <Compile Include="Plugin\Sal\AccountTransfer\Delete.cs" />
    <Compile Include="Plugin\Sal\AccountTransfer\Save.cs" />
    <Compile Include="Plugin\Sal\DealerSetup\Save.cs" />
    <Compile Include="Plugin\Sal\DealerSetup\ViewParam.cs" />
    <Compile Include="Plugin\ScheduleBillService.cs" />
    <Compile Include="Plugin\SER\Master\BindBankCard.cs" />
    <Compile Include="Plugin\SER\Master\GetMasterDetail.cs" />
    <Compile Include="Plugin\SER\Master\GetMasterList.cs" />
    <Compile Include="Plugin\SER\Master\ListBankCard.cs" />
    <Compile Include="Plugin\SER\Master\QueryTrans.cs" />
    <Compile Include="Plugin\SER\Master\UnbindBankCard.cs" />
    <Compile Include="Plugin\SER\MerchantOrderSettleDyn\Settle.cs" />
    <Compile Include="Plugin\SER\MerchantOrder\AcceptBill.cs" />
    <Compile Include="Plugin\SER\MerchantOrder\AcceptCancel.cs" />
    <Compile Include="Plugin\SER\MerchantOrder\CheckNewBill.cs" />
    <Compile Include="Plugin\SER\MerchantOrder\ConfirmPrice.cs" />
    <Compile Include="Plugin\SER\MerchantOrder\ConsumptionRecord.cs" />
    <Compile Include="Plugin\SER\MerchantOrder\Delete.cs" />
    <Compile Include="Plugin\SER\MerchantOrder\Writeback.cs" />
    <Compile Include="Plugin\SER\ServiceApply\UnAudit.cs" />
    <Compile Include="Plugin\SER\ServiceChange\Audit.cs" />
    <Compile Include="Plugin\SER\ServiceChange\Save.cs" />
    <Compile Include="Plugin\SER\ServiceChange\Unaudit.cs" />
    <Compile Include="Plugin\SER\ServiceChange\Unsubmit.cs" />
    <Compile Include="Plugin\SER\ServiceFeed\Delete.cs" />
    <Compile Include="Plugin\SER\Service\Delete.cs" />
    <Compile Include="Plugin\SER\Service\GetServiceList.cs" />
    <Compile Include="Plugin\SER\Service\ListServiceChange.cs" />
    <Compile Include="Plugin\SER\Service\LoadCaptain.cs" />
    <Compile Include="Plugin\SER\Service\Push2ServiceChange.cs" />
    <Compile Include="Plugin\SER\Service\Push2ServiceChangeConvertPlugIn.cs" />
    <Compile Include="Plugin\SER\Service\Save.cs" />
    <Compile Include="Plugin\SER\Service\Writeback.cs" />
    <Compile Include="Plugin\BAS\Building\Save.cs" />
    <Compile Include="Plugin\BAS\Building\InitBill.cs" />
    <Compile Include="Plugin\STE\CashBill\CashDelete.cs" />
    <Compile Include="Plugin\STE\CashBill\CashSave.cs" />
    <Compile Include="Plugin\STE\CashBill\WritebackAccountBalance.cs" />
    <Compile Include="Plugin\STE\CashBill\WritebackOrderRecvAmount.cs" />
    <Compile Include="Plugin\STE\Channel\Customer.cs" />
    <Compile Include="Plugin\STE\Channel\Save.cs" />
    <Compile Include="Plugin\STE\CostAccounting\Audit.cs" />
    <Compile Include="Plugin\STE\CostAccounting\OrderToCostaccountingConvertService.cs" />
    <Compile Include="Plugin\STE\CostAccounting\Save.cs" />
    <Compile Include="Plugin\STE\Customer\CanRechargeSynergy.cs" />
    <Compile Include="Plugin\STE\Customer\Copy.cs" />
    <Compile Include="Plugin\STE\Customer\CorrectBalance.cs" />
    <Compile Include="Plugin\STE\Customer\Delete.cs" />
    <Compile Include="Plugin\STE\Customer\LoadAccountBalance.cs" />
    <Compile Include="Plugin\STE\Customer\OpenCompany.cs" />
    <Compile Include="Plugin\STE\Customer\PurGetSynAccount.cs" />
    <Compile Include="Plugin\STE\Customer\Save.cs" />
    <Compile Include="Plugin\STE\DesignScheme\Delete.cs" />
    <Compile Include="Plugin\STE\DesignScheme\LoadData.cs" />
    <Compile Include="Plugin\STE\DesignScheme\Save.cs" />
    <Compile Include="Plugin\STE\Leads\Delete.cs" />
    <Compile Include="Plugin\STE\Leads\GetDetail.cs" />
    <Compile Include="Plugin\STE\Leads\GetLead.cs" />
    <Compile Include="Plugin\STE\Leads\GetLeads.cs" />
    <Compile Include="Plugin\STE\Leads\GetList.cs" />
    <Compile Include="Plugin\STE\Leads\GetReferenceFilters.cs" />
    <Compile Include="Plugin\STE\Leads\GetRescentCloseReason.cs" />
    <Compile Include="Plugin\STE\Leads\LeadsClaim.cs" />
    <Compile Include="Plugin\STE\Leads\LeadsClose.cs" />
    <Compile Include="Plugin\STE\Leads\LeadsReplace.cs" />
    <Compile Include="Plugin\STE\Leads\LeadsReturn.cs" />
    <Compile Include="Plugin\STE\Leads\LeadsToChance.cs" />
    <Compile Include="Plugin\STE\Leads\Save.cs" />
    <Compile Include="Plugin\STE\Leads\SaveRecord.cs" />
    <Compile Include="Plugin\STE\Leads\SearchLeads.cs" />
    <Compile Include="Plugin\STE\Order\Save.cs" />
    <Compile Include="Plugin\STE\OrderSettleDyn\Settle.cs" />
    <Compile Include="Plugin\STE\Order\AssignDesigner.cs" />
    <Compile Include="Plugin\STE\Order\AssignScale.cs" />
    <Compile Include="Plugin\STE\Order\Audit.cs" />
    <Compile Include="Plugin\STE\Order\BorrowGood.cs" />
    <Compile Include="Plugin\STE\Order\ConfirmCash.cs" />
    <Compile Include="Plugin\STE\Order\Copy.cs" />
    <Compile Include="Plugin\STE\Order\Cost.cs" />
    <Compile Include="Plugin\STE\Order\Delete.cs" />
    <Compile Include="Plugin\STE\Order\DynamicRuleImpl.cs" />
    <Compile Include="Plugin\STE\Order\GetDetail.cs" />
    <Compile Include="Plugin\STE\Order\GetList.cs" />
    <Compile Include="Plugin\STE\Order\GetOperateMode.cs" />
    <Compile Include="Plugin\STE\Order\LoadData.cs" />
    <Compile Include="Plugin\STE\Order\LoadSettleInfo.cs" />
    <Compile Include="Plugin\STE\Order\LookDesigner.cs" />
    <Compile Include="Plugin\STE\Order\LookScale.cs" />
    <Compile Include="Plugin\STE\Order\NewSettle.cs" />
    <Compile Include="Plugin\STE\Order\OrderLoadSyncInfoService.cs" />
    <Compile Include="Plugin\STE\Order\OrderSyncCheckOperationService.cs" />
    <Compile Include="Plugin\STE\Order\OutStock.cs" />
    <Compile Include="Plugin\STE\Order\PushPurOrder.cs" />
    <Compile Include="Plugin\STE\Order\PushSoStockOut.cs" />
    <Compile Include="Plugin\STE\Order\ReturnGood.cs" />
    <Compile Include="Plugin\STE\Order\StockOutNotify.cs" />
    <Compile Include="Plugin\STE\Order\SyncCheckOperation.cs" />
    <Compile Include="Plugin\STE\Order\SyncFSWidgetInitData.cs" />
    <Compile Include="Plugin\STE\Order\SyncFSWidgetUploadData.cs" />
    <Compile Include="Plugin\STE\Order\UnAudit.cs" />
    <Compile Include="Plugin\STE\SaleIntention\Accase.cs" />
    <Compile Include="Plugin\STE\SaleIntention\AssignDesigner.cs" />
    <Compile Include="Plugin\STE\SaleIntention\AssignScale.cs" />
    <Compile Include="Plugin\STE\SaleIntention\Audit.cs" />
    <Compile Include="Plugin\STE\SaleIntention\CancelAccase.cs" />
    <Compile Include="Plugin\STE\SaleIntention\CancelTransmit.cs" />
    <Compile Include="Plugin\STE\SaleIntention\ConfirmDeposit.cs" />
    <Compile Include="Plugin\STE\SaleIntention\Copy.cs" />
    <Compile Include="Plugin\STE\SaleIntention\Delete.cs" />
    <Compile Include="Plugin\STE\SaleIntention\DeleteSynergy.cs" />
    <Compile Include="Plugin\STE\SaleIntention\DynamicRuleImpl.cs" />
    <Compile Include="Plugin\STE\SaleIntention\GetDetail.cs" />
    <Compile Include="Plugin\STE\SaleIntention\GetList.cs" />
    <Compile Include="Plugin\STE\SaleIntention\LoadSettleInfo.cs" />
    <Compile Include="Plugin\STE\SaleIntention\LookDesigner.cs" />
    <Compile Include="Plugin\STE\SaleIntention\LookScale.cs" />
    <Compile Include="Plugin\STE\SaleIntention\NewSettle.cs" />
    <Compile Include="Plugin\STE\SaleIntention\Save.cs" />
    <Compile Include="Plugin\STE\SaleIntention\SaveOrderService.cs" />
    <Compile Include="Plugin\STE\SaleIntention\SaveSynergy.cs" />
    <Compile Include="Plugin\STE\SaleIntention\Submit.cs" />
    <Compile Include="Plugin\STE\SaleIntention\Transmit.cs" />
    <Compile Include="Plugin\STE\SaleIntention\UpdateBizStatus.cs" />
    <Compile Include="Plugin\STE\SaleIntention\UpdateSyn.cs" />
    <Compile Include="Plugin\STE\SaleIntention\UpdateSynBizStatus.cs" />
    <Compile Include="Plugin\STE\SaleSettleDyn\GetAccountBalance.cs" />
    <Compile Include="Plugin\STE\SaleSettleDyn\Settle.cs" />
    <Compile Include="Plugin\STE\ScaleRecord\Delete.cs" />
    <Compile Include="Plugin\STE\ScaleRecord\Save.cs" />
    <Compile Include="Plugin\STE\StoreSysParam\LoadStoreSysParam.cs" />
    <Compile Include="Plugin\STK\Supplier\Delete.cs" />
    <Compile Include="Plugin\STK\InventoryList\ClearZeroStock.cs" />
    <Compile Include="Plugin\STK\InventoryList\GetInventory.cs" />
    <Compile Include="Plugin\STK\PoStockIn\Audit.cs" />
    <Compile Include="Plugin\STK\PoStockIn\UnAudit.cs" />
    <Compile Include="Plugin\STK\PurReceiptNotice\FeeDistribution.cs" />
    <Compile Include="Plugin\STK\ScheduleApply\Save.cs" />
    <Compile Include="Plugin\STK\ScheduleApply\UnAudit.cs" />
    <Compile Include="Plugin\STK\SchedulePlanBill\Delete.cs" />
    <Compile Include="Plugin\STK\SchedulePlatform\ConfirmSchedulePlan.cs" />
    <Compile Include="Plugin\STK\SchedulePlatform\GetScheduleEntity.cs" />
    <Compile Include="Plugin\STK\SchedulePlatform\GetSchedulePlanBillTree.cs" />
    <Compile Include="Plugin\STK\SchedulePlatform\GetServiceEntity.cs" />
    <Compile Include="Plugin\STK\SchedulePlatform\GetWatingSchedulePlan.cs" />
    <Compile Include="Plugin\STK\SchedulePlatform\GetWatingSchedulePlanEntry.cs" />
    <Compile Include="Plugin\STK\SchedulePlatform\HasSchedulePlan.cs" />
    <Compile Include="Plugin\STK\SchedulePlatform\SaveScheduleBillEntry.cs" />
    <Compile Include="Plugin\STK\SchedulePlatform\SaveSchedulePlanBill.cs" />
    <Compile Include="Plugin\STK\SchedulePlatform\SaveServiceEntity.cs" />
    <Compile Include="Plugin\BAS\Schedule\GetScheduleTimeList.cs" />
    <Compile Include="Plugin\STK\SoStockOut\Audit.cs" />
    <Compile Include="Plugin\STK\SoStockOut\Delete.cs" />
    <Compile Include="Plugin\STK\SoStockOut\StockOutNotifyService.cs" />
    <Compile Include="Plugin\STK\SoStockOut\UnAudit.cs" />
    <Compile Include="Plugin\STK\StockParam\StockParamSave.cs" />
    <Compile Include="Plugin\STK\StockParam\LoadStockSysParam.cs" />
    <Compile Include="Plugin\STK\StoreHouse\Save.cs" />
    <Compile Include="Plugin\STK\Supplier\CheckIsSyn.cs" />
    <Compile Include="Plugin\STK\Supplier\Copy.cs" />
    <Compile Include="Plugin\STK\Supplier\GetRechargeAccounts.cs" />
    <Compile Include="Plugin\STK\Supplier\LoadAccountBalance.cs" />
    <Compile Include="Plugin\STK\Supplier\Save.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\CustomerRecordClaim.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\CustomerRecordClose.cs" />
    <Compile Include="Plugin\BAS\Record\SaveLog.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\CustomerRecordReplace.cs" />
    <Compile Include="Plugin\Sal\SoReturnDialog\New.cs" />
    <Compile Include="Plugin\SER\ComplaintRecord\UpdateStatus.cs" />
    <Compile Include="Plugin\SER\Dealer\Save.cs" />
    <Compile Include="Plugin\SER\SeritemPrice\Save.cs" />
    <Compile Include="Plugin\SER\Reward\Save.cs" />
    <Compile Include="Plugin\SER\ServiceChange\QueryData.cs" />
    <Compile Include="Plugin\SER\Service\ServiceData.cs" />
    <Compile Include="Plugin\SER\ServiceFeed\Save.cs" />
    <Compile Include="Plugin\SER\Service\LoadCate.cs" />
    <Compile Include="Plugin\SER\Service\SendSms.cs" />
    <Compile Include="Plugin\SER\Service\QueryAmount.cs" />
    <Compile Include="Plugin\SER\Service\TakeCash.cs" />
    <Compile Include="Plugin\SER\Team\Save.cs" />
    <Compile Include="Plugin\SER\TruckInfo\Save.cs" />
    <Compile Include="Plugin\SHT\Order\InitBill.cs" />
    <Compile Include="Plugin\STK\InventoryList\LoadInventoryList.cs" />
    <Compile Include="Plugin\STK\InventoryVerify\Copy.cs" />
    <Compile Include="Plugin\Synergism\CoocompanyInfo.cs" />
    <Compile Include="Plugin\Synergism\syn_Product.cs" />
    <Compile Include="Plugin\Synergism\syn_Supplier.cs" />
    <Compile Include="Plugin\Synergism\syn_Customer.cs" />
    <Compile Include="Plugin\WeChat\CreateCode.cs" />
    <Compile Include="Plugin\WeChat\Group.cs" />
    <Compile Include="Plugin\WeChat\Menu.cs" />
    <Compile Include="Plugin\WeChat\MicroMembers.cs" />
    <Compile Include="Plugin\STE\Customer\TotalData.cs" />
    <Compile Include="Plugin\STE\Bespeak\Save.cs" />
    <Compile Include="Plugin\Synergism\syn_Company.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\CustomerRecordGetDetail.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\CustomerRecordGetList.cs" />
    <Compile Include="Plugin\FIN\SettleOrder\Save.cs" />
    <Compile Include="Plugin\STE\Goal\Save.cs" />
    <Compile Include="Plugin\SER\Team\GetMasterListByMasterId.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\Copy.cs" />
    <Compile Include="Plugin\SER\Dealerinfo\Save.cs" />
    <Compile Include="Plugin\BAS\Product\GoodsToPricing.cs" />
    <Compile Include="Plugin\SER\Master\SearchMaster.cs" />
    <Compile Include="Plugin\SER\Service\ServiceSave.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\CustomerRecordDelete.cs" />
    <Compile Include="Plugin\STE\CustomerRecord\CustomerRecordGenerate.cs" />
    <Compile Include="Plugin\BAS\Product\ProductPricing.cs" />
    <Compile Include="PriceService.cs" />
    <Compile Include="ProductImageService.cs" />
    <Compile Include="ProductInfoService.cs" />
    <Compile Include="ProductPriceService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PurchaseOrderService.cs" />
    <Compile Include="RegInterventionEvent.cs" />
    <Compile Include="Report\CustomerCapital.cs" />
    <Compile Include="Report\IncomeDisburseChart.cs" />
    <Compile Include="Report\IncomeDisburseChartCustomer.cs" />
    <Compile Include="Report\IncomeDisburseChartPur.cs" />
    <Compile Include="Report\IncomeDisburseChartSal.cs" />
    <Compile Include="Report\IndexOverviewQuery.cs" />
    <Compile Include="Report\OrderExecute\QueryListReport.cs" />
    <Compile Include="Report\OrderExecute\QueryListReportData.cs" />
    <Compile Include="Report\ProfitAnalysis\QueryListReportData.cs" />
    <Compile Include="Report\PurchaseExecute\QueryListReport.cs" />
    <Compile Include="Report\PurchaseExecute\QueryListReportData.cs" />
    <Compile Include="Report\RptBaseData.cs" />
    <Compile Include="Report\SaleAchievesChart.cs" />
    <Compile Include="Report\SaleGoalsRatingChart.cs" />
    <Compile Include="Report\SalemanRankChart.cs" />
    <Compile Include="Report\StockdetailExecute\QueryListReport.cs" />
    <Compile Include="Report\StockdetailExecute\QueryListReportData.cs" />
    <Compile Include="Report\SupplierCapital.cs" />
    <Compile Include="Report\IncomeDisburseChartSupplier.cs" />
    <Compile Include="SaleIntentionService.cs" />
    <Compile Include="ServiceInst\AccountBalanceUpdateService.cs" />
    <Compile Include="MuSi\MuSiClient.cs" />
    <Compile Include="MuSi\Service\MuSiBizObjMapService.cs" />
    <Compile Include="Service\PDA\PadResponse.cs" />
    <Compile Include="Service\ProductAuthService.cs" />
    <Compile Include="Service\PromotionCombineService.cs" />
    <Compile Include="Service\Promotion\OrderPromotionService_ProductPromotion.cs" />
    <Compile Include="Service\Promotion\OrderPromotionService_ComboPromotion.cs" />
    <Compile Include="Service\Promotion\OrderPromotionService.cs" />
    <Compile Include="Service\Promotion\PromotionUtil.cs" />
    <Compile Include="Service\PurOrderChgAutoAuditService.cs" />
    <Compile Include="Service\RepairService.cs" />
    <Compile Include="Service\StoreService.cs" />
    <Compile Include="Service\AgentService.cs" />
    <Compile Include="Service\StatementService.cs" />
    <Compile Include="Service\SyncProductVolumeService.cs" />
    <Compile Include="SupplierService.cs" />
    <Compile Include="LeftJoinWithIndex.cs" />
    <Compile Include="SynergyService.cs" />
    <Compile Include="Clients\Ewc\Utils\UserHelper.cs" />
    <Compile Include="Utils\CheckDateTimeHelper.cs" />
    <Compile Include="Utils\CheckPhoneNum.cs" />
    <Compile Include="Utils\CreateContextUtil.cs" />
    <Compile Include="Utils\HtmlFormExtentions.cs" />
    <Compile Include="Utils\HttpRequestUtils.cs" />
    <Compile Include="MuSi\Utils\MuSiUtil.cs" />
    <Compile Include="Utils\MySqlHelper.cs" />
    <Compile Include="Utils\StringFormatValidation.cs" />
    <Compile Include="Validation\BAS\Agent\SaveValidation.cs" />
    <Compile Include="Validation\BAS\SalePrice\SalePriceTypeValidation.cs" />
    <Compile Include="Validation\BD\BillType\SaveValidation.cs" />
    <Compile Include="Validation\IncomedisburseValidation.cs" />
    <Compile Include="Validation\OtherStockInUnAuditValidation.cs" />
    <Compile Include="Validation\InventoryVerifyUnAuditValidation.cs" />
    <Compile Include="Validation\PostockReturnUnAuditValidation.cs" />
    <Compile Include="Validation\DesignScaleDeleteValidation.cs" />
    <Compile Include="Validation\ProductSuiteValidation.cs" />
    <Compile Include="Validation\PropValueNameValidation.cs" />
    <Compile Include="Validation\Pur\PurchaseOrderApplyChg\PurOrderApplyChgSaveValidation.cs" />
    <Compile Include="Validation\Pur\PurchaseOrder\InitiateChangeApplyValidation.cs" />
    <Compile Include="Validation\SelCategoryPropValueValidation.cs" />
    <Compile Include="Validation\SelProductAuxPropValidation.cs" />
    <Compile Include="Validation\SelRangePropValueValidation.cs" />
    <Compile Include="Validation\OrderChangeStatusValidation.cs" />
    <Compile Include="Validation\PostockInUnAuditValidation.cs" />
    <Compile Include="Validation\SostockOutUnAuditValidation.cs" />
    <Compile Include="Validation\SourceBillChangeStatusValidation.cs" />
    <Compile Include="Validation\STE\Order\SaveValidation.cs" />
    <Compile Include="Validation\STE\Order\UnstdPriceValidation.cs" />
    <Compile Include="Validation\STK\AuditOrUnauditValidation.cs" />
    <Compile Include="Validation\STK\InventoryVerify\SaveValidation.cs" />
    <Compile Include="Validation\StockReturnSaveValidation.cs" />
    <Compile Include="Validation\StockSaveValidation.cs" />
    <Compile Include="Validation\SuiteProductCombNoValidation.cs" />
    <Compile Include="WarmUpService\InitCacheData.cs" />
    <Compile Include="WarmUpService\ReleaseExpirerReserve.cs" />
    <Compile Include="WeixinService.cs" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Plugin\AFT\售后管理.txt" />
    <Content Include="Plugin\Auth\readme.txt" />
    <Content Include="Plugin\BAS\基础管理.txt" />
    <Content Include="Plugin\BCM\条码管理.txt" />
    <Content Include="Plugin\BillConvert\单据转换.txt" />
    <Content Include="Plugin\Dashboard\首页仪表盘.txt" />
    <Content Include="Plugin\FIN\财务管理.txt" />
    <Content Include="Plugin\MobileShop\门店APP移动端.txt" />
    <Content Include="Plugin\Pur\采购管理.txt" />
    <Content Include="Plugin\Sal\销售管理.txt" />
    <Content Include="Plugin\SER\服务管理.txt" />
    <Content Include="Plugin\SHT\商户通.txt" />
    <Content Include="Plugin\STE\店面管理.txt" />
    <Content Include="Plugin\BAS\Schedule\日程.txt" />
    <Content Include="Plugin\STK\库存管理.txt" />
    <Content Include="Plugin\Synergism\协同管理.txt" />
    <Content Include="Plugin\WeChat\微信.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\JieNor.AMS.YDJ.MP.API\JieNor.AMS.YDJ.MP.API.csproj">
      <Project>{18E7268B-D122-4398-A796-270764724832}</Project>
      <Name>JieNor.AMS.YDJ.MP.API</Name>
    </ProjectReference>
    <ProjectReference Include="..\JieNor.AMS.YDJ.MS.API\JieNor.AMS.YDJ.MS.API.csproj">
      <Project>{abe7ba67-d93b-5a87-e6b8-d6d4dd03601a}</Project>
      <Name>JieNor.AMS.YDJ.MS.API</Name>
    </ProjectReference>
    <ProjectReference Include="..\JieNor.AMS.YDJ.SWJ.API\JieNor.AMS.YDJ.SWJ.API.csproj">
      <Project>{065761D1-29F7-491D-ABF0-30A8D7872226}</Project>
      <Name>JieNor.AMS.YDJ.SWJ.API</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Plugin\BAS\NewFolder1\" />
    <Folder Include="Plugin\Sys\MsgserEditor\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
  </PropertyGroup>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <Import Project="..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets" Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" />
  <Target Name="EnsureBclBuildImported" BeforeTargets="BeforeBuild" Condition="'$(BclBuildImported)' == ''">
    <Error Condition="!Exists('..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" Text="This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=317567." HelpKeyword="BCLBUILD2001" />
    <Error Condition="Exists('..\packages\Microsoft.Bcl.Build.1.0.14\tools\Microsoft.Bcl.Build.targets')" Text="The build restored NuGet packages. Build the project again to include these packages in the build. For more information, see http://go.microsoft.com/fwlink/?LinkID=317568." HelpKeyword="BCLBUILD2002" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <!-- <PropertyGroup>
    <PostBuildEvent>xcopy /e /r /y "$(TargetDir)$(TargetName).dll" "$(SolutionDir)JieNor.AMS.YDJ.Store.Web\bin\"
xcopy /e /r /y "$(TargetDir)$(TargetName).pdb" "$(SolutionDir)JieNor.AMS.YDJ.Store.Web\bin\"</PostBuildEvent>
  </PropertyGroup> -->
</Project>