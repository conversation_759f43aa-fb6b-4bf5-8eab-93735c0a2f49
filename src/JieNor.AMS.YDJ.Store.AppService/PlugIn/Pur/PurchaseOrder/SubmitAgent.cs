using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.Validator;
using JieNor.Framework.MetaCore.FormOp.FormService.PlugIn;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.CustomException;
using JieNor.Framework.MetaCore.FormOp;
using JieNor.Framework.MetaCore.FormOp.FormService;
using JieNor.Framework.SuperOrm;
using JieNor.Framework.Interface.Log;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.AMS.YDJ.Store.AppService.Plugin.STE.Order;
using Newtonsoft.Json.Linq;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.DataTransferObject.Enums;
using JieNor.Framework.Consts;
using JieNor.Framework.Enums;

namespace JieNor.AMS.YDJ.Store.AppService.Plugin.Pur.PurchaseOrder
{
    /// <summary>
    /// 采购订单：提交一级经销
    /// </summary>
    [InjectService]
    [FormId("ydj_purchaseorder")]
    [OperationNo("SubmitAgent")]
    public class SubmitAgent : AbstractOperationServicePlugIn
    {
        /// <summary>
        /// “可用”库存状态常量
        /// </summary>
        private const string KCZT_SYS_01 = "311858936800219137";

        /// <summary>
        /// “大客户采购订单”单据类型常量
        /// </summary>
        private const string DKHCGDD_SYS_01 = "ydj_purchaseorder_type";

        /// <summary>
        /// “大客户销售合同”单据类型常量
        /// </summary>
        private const string DKHXSHT_SYS_01 = "ydj_order_major";

        /// <summary>
        /// “标准销售合同”单据类型常量
        /// </summary>
        private const string BTXSHT_SYS_01 = "order_billtype_01";

        /// <summary>
        /// “大客户销售合同”单据类型ID
        /// </summary>
        private string DkhxshtBillTypeId { get; set; }

        /// <summary>
        /// “门店上样”单据类型ID
        /// </summary>
        private string MdsyBillTypeId { get; set; }

        /// <summary>
        /// “标准销售合同”单据类型ID
        /// </summary>
        private string BtxshtBillTypeId { get; set; }

        private string pricerule { get; set; } = "0";

        /// <summary>
        /// 采购订单单据类型
        /// </summary>
        private DynamicObjectCollection PurBillTypeObjs { get; set; }

        /// <summary>
        /// 当前二级经销商对应的一级经销商企业ID
        /// </summary>
        private string ParentAgentId { get; set; }

        /// <summary>
        /// 当前二级经销商对应的一级经销商企业用户上下文
        /// </summary>
        private UserContext ParentAgentCtx { get; set; }

        /// <summary>
        /// 当前二级经销商对应的客户ID
        /// </summary>
        private string CurrentAgentCustomerId { get; set; }

        /// <summary>
        /// 当前二级经销商对应的客户
        /// </summary>
        private DynamicObject CurrentAgentCustomer { get; set; }

        /// <summary>
        /// 当前二级经销商是否不管理库存
        /// </summary>
        private bool IsNotMgrInv { get; set; }

        /// <summary>
        /// 当前一级经销商是否启用来源门店
        /// </summary>
        private bool IsUseSrcStroe { get; set; }

        /// <summary>
        /// 当前二级经销商上的销售对接人ID
        /// </summary>
        private string SaleManId { get; set; }

        /// <summary>
        /// 当前二级经销商上的销售对接人的部门ID
        /// </summary>
        private string SaleManDeptId { get; set; }

        /// <summary>
        /// 当前一级经销商客户档案的来源门店ID
        /// </summary>
        private string SrcStoreId { get; set; }

        ///// <summary>
        ///// 当前二级经销商上的分销价格系数
        ///// </summary>
        //private decimal ResellRatio { get; set; }

        /// <summary>
        /// 销售合同表单模型
        /// </summary>
        private HtmlForm OrderForm { get; set; }

        /// <summary>
        /// 当前二级分销信息
        /// </summary>

        private DynamicObjectCollection currentAgentInfo { get; set; }

        /// <summary>
        /// 采购订单关联的销售合同数据包
        /// </summary>
        private List<DynamicObject> sourceOrderObjs { get; set; }

        /// <summary>
        /// 采购订单关联的销售合同的销售员信息
        /// key:销售合同编码
        /// value：销售员信息
        /// </summary>
        private Dictionary<string,List<OrderStaffInfo>> orderInfoDic { set; get; }

        /// <summary>
        /// 初始化服务插件上下文时触发的事件
        /// </summary>
        /// <param name="operCtx"></param>
        /// <param name="serviceList"></param>
        public new void InitializeOperationContext(OperationContext operCtx, params object[] serviceList)
        {
            // 启用幂等性检查
            var serCtrlOpt = serviceList.FirstOrDefault(o => o is ServiceControlOption) as ServiceControlOption;
            serCtrlOpt.SupportIdemotency = true;

            base.InitializeOperationContext(operCtx, serviceList);
        }

        /// <summary>
        /// 准备操作选项时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void OnPrepareOperationOption(OnPrepareOperationOptionEventArgs e)
        {
            base.OnPrepareOperationOption(e);

            e.OpCtlParam.IgnoreOpMessage = true;
        }

        /// <summary>
        /// 预处理校验规则时触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void PrepareValidationRules(PrepareValidationRulesEventArgs e)
        {
            base.PrepareValidationRules(e);

            e.Rules.Add(new SubmitAgentValidation());

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                if (!this.Context.IsSecondOrg)
                {
                    return false;
                }
                return true;
            }).WithMessage("当前用户不是二级分销商, 不允许提交一级经销！"));

            var errorMessage = "";

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                errorMessage = "";
                if (!Convert.ToString(newData["fstatus"]).EqualsIgnoreCase("E"))
                {
                    errorMessage = $"{this.HtmlForm.Caption}【{newData[this.HtmlForm.GetNumberField().PropertyName]}】不是已审核，不允许提交一级经销！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));

            e.Rules.Add(this.RuleFor("fbillhead", data => data).IsTrue((newData, oldData) =>
            {
                //【一级合同号】为空或【一级合同状态】为驳回时才可以提交，否则不允许提交。
                var canSubmit =
                    newData["fonelvoderno"].IsNullOrEmptyOrWhiteSpace() ||
                    Convert.ToString(newData["fonelvoderstatus"]).EqualsIgnoreCase(OneLvOrderSratusConst.Reject);
                if (!canSubmit)
                {
                    errorMessage = $"{this.HtmlForm.Caption}【{newData[this.HtmlForm.GetNumberField().PropertyName]}】已提交至总部或不是驳回，不允许重复提交！";
                    return false;
                }
                return true;
            }).WithMessage("{0}", (billObj, propObj) => errorMessage));
        }

        /// <summary>
        /// 调用操作事物后触发的事件
        /// </summary>
        /// <param name="e"></param>
        public override void EndOperationTransaction(EndOperationTransactionArgs e)
        {
            base.EndOperationTransaction(e);

            if (e.DataEntitys == null || !e.DataEntitys.Any()) return;


            var currAgentSql = $@"
                select t1.fisreseller,t1.fisnotmgrinv,t1.fsalemanid,t1.fresellratio,
				t2.fseriesid,t2.fresellratio fresellratio_e,t1.fpricerule
				from t_bas_agent t1 with(nolock)
				left join t_agent_ratioinfo t2  with(nolock) on t1.fid=t2.fid
				where t1.fid='{this.Context.Company}'";

            currentAgentInfo = this.Context.ExecuteDynamicObject(currAgentSql, null);
            var currentAgent = currentAgentInfo.FirstOrDefault();
            if (currentAgent == null)
            {
                var companyObj = this.Context.Companys?.FirstOrDefault(o => o.CompanyId.EqualsIgnoreCase(this.Context.Company));
                var companyInfo = this.Context.Company;
                if (companyObj != null)
                {
                    companyInfo = $"{companyObj?.CompanyNumber}/{companyObj?.CompanyName}";
                }
                this.Result.ComplexMessage.WarningMessages.Add(
                    $"当前经销商【{companyInfo}】不存在或已被删除，无法提交一级经销！");
                return;
            }
            this.pricerule = Convert.ToString(currentAgent?["fpricerule"]);
            var saleManId = Convert.ToString(currentAgent?["fsalemanid"]);
            var isReseller = Convert.ToString(currentAgent?["fisreseller"]) == "1";
            if (!isReseller)
            {
                this.Result.ComplexMessage.WarningMessages.Add(
                    "当前经销商不是二级分销商，不允许提交一级经销！");
                return;
            }
            if (saleManId.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.ComplexMessage.WarningMessages.Add(
                    "当前二级分销商档案没有录入【销售对接人】，不允许提交一级经销！");
                return;
            }
            this.SaleManId = saleManId;
            this.IsNotMgrInv = Convert.ToString(currentAgent?["fisnotmgrinv"]) == "1";
            //this.ResellRatio = Convert.ToDecimal(currentAgent?["fresellratio"])<=0?1: Convert.ToDecimal(currentAgent?["fresellratio"]);

            // 加载供应商销售组织的上级组织ID
            var supplierObjs = SubmitAgentValidation.LoadSupplierParentOrgIds(this.Context, e.DataEntitys);
            if (supplierObjs?.Count > 1)
            {
                this.Result.ComplexMessage.WarningMessages.Add(
                    $"{this.HtmlForm.Caption}的供应商的销售组织存在多个，无法确定一级经销商！");
                return;
            }

            // 一级经销商组织ID
            this.ParentAgentId = Convert.ToString(supplierObjs?.FirstOrDefault()?["fagentid"]);
            if (this.ParentAgentId.IsNullOrEmptyOrWhiteSpace())
            {
                this.Result.ComplexMessage.WarningMessages.Add(
                    $"{this.HtmlForm.Caption}的供应商的销售组织不是一级经销商，不允许提交一级经销！");
                return;
            }

            // 加载二级分销商【销售对接人】的部门ID
            var saleManDeptObj = this.Context.LoadBizBillHeadDataById("ydj_staff", saleManId, "fdeptid");
            this.SaleManDeptId = Convert.ToString(saleManDeptObj?["fdeptid"]);

            this.OrderForm = this.MetaModelService.LoadFormModel(this.Context, "ydj_order");

            // 创建一级经销商企业用户上下文
            this.ParentAgentCtx = this.Context.CreateAgentDBContext(this.ParentAgentId);

            // 加载当前二级经销商对应的客户ID
            this.LoadCurrentAgentCustomerId();

            //获取一级经销商的《销售管理参数》中<客户报备唯一性规则>参数

            var paramSer = this.Container.GetService<ISystemProfile>();
            var fcustomerunique = paramSer.GetSystemParameter<string>(this.ParentAgentCtx, "bas_storesysparam", "fcustomerunique");
            if (fcustomerunique != null && fcustomerunique.IndexOf("store") > 0)
            {
                var fsrcstoreid = (string)this.CurrentAgentCustomer?["fsrcstoreid"] ?? "";
                if (fsrcstoreid.IsNullOrEmptyOrWhiteSpace())
                {
                    this.Result.ComplexMessage.WarningMessages.Add(
                        $" 对不起，一级经销商客户档案未维护【来源门店】，禁止提交。");
                    return;
                }
                else this.IsUseSrcStroe = true;
            }
            else
            {
                this.IsUseSrcStroe = false;
            }

            // 批量加载采购订单上游销售合同信息
            var sourceOrderObjs = this.LoadSourceOrder(e.DataEntitys);

            // 批量加载采购订单已提交过的一级经销商销售合同数据包
            var agentOrderObjs = this.LoadAgentOrders(e.DataEntitys);

            // 批量加载采购订单商品信息
            var productObjs = this.LoadProducts(e.DataEntitys);

            // 批量加载一级经销商定义的商品二级分销价格
            var productPrices = this.LoadProductResellerPrice(e.DataEntitys);

            //批量加载商品总部采购价目
            var hqPurPrices = this.LoadProductStandPrice(e.DataEntitys);

            // 上游销售合同销售部门ID
            var sourceOrderDeptKv = new Dictionary<string, string>();
            foreach (var item in e.DataEntitys)
            {
                //无需从销售合同取部门，直接取当前二级采购订单的采购部门
                sourceOrderDeptKv[Convert.ToString(item["fbillno"])] = Convert.ToString(item["fpodeptid"]);
            }
            var sourceOrderDeptIds = sourceOrderDeptKv.Values;

            // 当前没有源单编号的采购订单的采购部门ID
            var purDeptIds = e.DataEntitys
                .Where(o => o["fsourcenumber"].IsNullOrEmptyOrWhiteSpace())
                .Select(o => Convert.ToString(o["fpodeptid"]));

            // 批量加载二级分销商的部门所关联的门店的来源门店的部门
            // 根据部门 => 找门店 => 找来源门店 => 找部门（一级经销商的部门）可能会找到多个？？？
            var subAgentDeptIds = new List<string>(sourceOrderDeptIds);
            subAgentDeptIds.AddRange(purDeptIds);
            //var agentDepts = this.LoadAgentDepts(subAgentDeptIds);
            //逻辑重构
            var agentDepts = this.LoadTopAgentDept(subAgentDeptIds);

            // 加载一级经销商【标准销售合同】单据类型
            var btxshtBillType = this.ParentAgentCtx.GetNotForbidBillTypeByBizObject(this.OrderForm.Id, BTXSHT_SYS_01);
            this.BtxshtBillTypeId = btxshtBillType?.fid;

            // 加载一级经销商【大客户销售合同】单据类型
            var dkhxshtBillType = this.ParentAgentCtx.GetNotForbidBillTypeByBizObject(this.OrderForm.Id, DKHXSHT_SYS_01);
            this.DkhxshtBillTypeId = dkhxshtBillType?.fid;

            // 加载一级经销商【门店上样】单据类型
            var mdsyBillType = this.ParentAgentCtx.GetNotForbidBillTypeByBizObject(this.OrderForm.Id, "ydj_order_mdsy");
            this.MdsyBillTypeId = mdsyBillType?.fid;

            // 批量加载采购订单单据类型
            var purBillTypeIds = e.DataEntitys
                .Select(o => Convert.ToString(o["fbilltypeid"]))
                .Distinct()
                .ToList();
            this.PurBillTypeObjs = this.Context.LoadBizBillHeadDataById("bd_billtype", purBillTypeIds, "fname");

            var orderDatas = new List<DynamicObject>();
            this.Context.Container.GetService<LoadReferenceObjectManager>().Load(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context), e.DataEntitys, true);

            //获取采购订单源单信息
            this.GetSourceOrderList(e.DataEntitys);
            foreach (var purOrderData in e.DataEntitys)
            {
                var orderData = this.BuildOrderData(purOrderData, agentOrderObjs, sourceOrderObjs, sourceOrderDeptKv, agentDepts, productObjs, productPrices, hqPurPrices);
                orderDatas.Add(orderData);
            }

            // 保存销售合同
            var successOrderDatas = this.SaveOrders(orderDatas);

            // 反写采购订单
            this.SavePurchaseOrders(e.DataEntitys, successOrderDatas);

            //生成一级合同后，自动调用提交、审核一级<一键采购>功能。
            this.AutoSubmitPur(orderDatas);

            this.AddRefreshPageAction();
            this.Result.IsSuccess = true;
        }

        /// <summary>
        /// 构建销售合同数据包
        /// </summary>
        /// <param name="purOrderData">采购订单数据包</param>
        /// <param name="agentOrderObjs">已提交过的一级经销商销售合同数据包</param>
        /// <param name="sourceOrderObjs">采购订单上游销售合同信息</param>
        /// <param name="sourceOrderDeptKv">采购订单上游销售合同部门</param>
        /// <param name="agentDepts">一级经销商部门</param>
        /// <param name="productObjs">商品信息</param>
        /// <param name="productPrices">商品价格</param>
        /// <param name="hqPurPrices">商品总部采购价目</param>
        /// <returns>销售合同数据包</returns>
        private DynamicObject BuildOrderData(
            DynamicObject purOrderData,
            List<DynamicObject> agentOrderObjs,
            List<DynamicObject> sourceOrderObjs,
            Dictionary<string, string> sourceOrderDeptKv,
            List<DynamicObject> agentDepts,
            DynamicObjectCollection productObjs,
            List<JObject> productPrices,
            List<JToken> hqPurPrices)
        {
            var purOrderId = Convert.ToString(purOrderData["id"]);

            // 不存在则新增，已存在则覆盖
            var orderData = agentOrderObjs?.FirstOrDefault(o => Convert.ToString(o["fsourceid"]).EqualsIgnoreCase(purOrderId));
            if (orderData == null)
            {
                orderData = new DynamicObject(this.OrderForm.GetDynamicObjectType(this.Context));
                orderData["fclosestatus"] = CloseStatusConst.Default;
            }
            else
            {
                // 清除作废相关字段值
                orderData["fcancelid"] = "";
                orderData["fcancelstatus"] = false;
                orderData["fcanceldate"] = null;
            }

            orderData["fsourcetype"] = this.HtmlForm.Id;
            orderData["fsourceid"] = purOrderId;
            orderData["fsourcenumber"] = purOrderData["fbillno"];
            orderData["fdeliverydate"] = purOrderData["fpickdate"];
            orderData["fdescription"] = purOrderData["fdescription"];
            orderData["fisresellorder"] = true;
            orderData["fmemberdesc"] = purOrderData["fmemberdesc"];

            var purBillTypeId = Convert.ToString(purOrderData["fbilltypeid"]);
            var purBillTypeObj = this.PurBillTypeObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(purBillTypeId));
            var purBillTypeName = Convert.ToString(purBillTypeObj?["fname"]).Trim();
            if (purBillTypeName.EqualsIgnoreCase("大客户采购订单"))
            {
                orderData["fbilltype"] = this.DkhxshtBillTypeId; // 单据类型为【大客户销售合同】
            }
            else//摆场订单
            if (purBillTypeName.EqualsIgnoreCase("摆场订单"))
            {
                orderData["fbilltype"] = this.MdsyBillTypeId; // 单据类型为【门店上样】
            }
            else
            {
                orderData["fbilltype"] = this.BtxshtBillTypeId; // 单据类型为【标准销售合同】
            }

            var orderNo = Convert.ToString(purOrderData["forderno"]);
            var sourceOrderObjReal = sourceOrderObjs;
            var sourceOrderObj = sourceOrderObjs?.FirstOrDefault(o => Convert.ToString(o["forderno"]).EqualsIgnoreCase(orderNo));
            var subAgentDeptId = "";

            // 是否是二级分销商备货采购：如果是二级分销备货的采购订单（无销售合同源单）
            var isStockUp = purOrderData["fsourcenumber"].IsNullOrEmptyOrWhiteSpace();
            //if (isStockUp)
            //{
            //    // 如果是二级分销商备货采购，则销售部门取当前二级分销商采购订单的采购部门
            //    subAgentDeptId = Convert.ToString(purOrderData["fpodeptid"]);
            //}
            //else
            //{
            //    // 如果不是二级分销商备货采购，则销售部门取当前二级分销商采购订单上游销售合同的销售部门
            //    subAgentDeptId = sourceOrderDeptKv.GetValue(orderNo);
            //}
            //
            //二级采购提交一级销售合同的销售部门取值逻辑：
            //原逻辑: 非备货二级采购有源合同，则取销售合同部门，找部门
            //现逻辑: 无论是否备货统一取二级采购的采购部门，找部门 （如果找到一级专属部门则取专属部门，找不到则按采购部门原逻辑去找对应的部门）
            subAgentDeptId = Convert.ToString(purOrderData["fpodeptid"]);
            var agentDeptId = "";
            if (this.IsUseSrcStroe)//已维护【来源门店】生成销售合同的【销售部门】直接取《客户》档案【来源门店】填充即可
            {
                agentDeptId = (string)(this.CurrentAgentCustomer?["fsrcstoreid"] ?? "");
            }
            else//未启用【来源门店】保持现有逻辑执行
            {
                // 销售部门取二级分销商部门关联门店对应的来源门店（也就是一级经销商的门店）
                var agentDept = agentDepts?.FirstOrDefault(o => Convert.ToString(o["fsubagentdeptid"]).EqualsIgnoreCase(subAgentDeptId));
                agentDeptId = Convert.ToString(agentDept?["fagentdeptid"]);
                if (agentDeptId.IsNullOrEmptyOrWhiteSpace())
                {
                    agentDeptId = this.SaleManDeptId;
                }
            }


            ////终端会员id，取二级合同上的终端会员id
            //orderData["fsecmemberno"] = sourceOrderObj?["fmemberno"] ?? "";

            orderData["fdeptid"] = agentDeptId;
            var store = Convert.ToString(this.Context.LoadBizBillHeadDataById("ydj_dept", agentDeptId, "fstore")?["fstore"]);
            orderData["fstore"] = store;

            // 销售员取二级经销商档案上的【销售对接人】
            orderData["fstaffid"] = this.SaleManId;

            // 客户为“二级经销商”对应的客户
            orderData["fcustomerid"] = this.CurrentAgentCustomerId;
            orderData["fcustomersource"]= this.CurrentAgentCustomer?["fsource"].ToString();

            //勾选上【需转单】时
            if (Convert.ToBoolean(purOrderData["fneedtransferorder"]))
            {
                orderData["fcustomerid"] = sourceOrderObj?["fcustomerid"] ?? "";
                orderData["fdescription"] = sourceOrderObj?["fdescription"] ?? "";
                orderData["flogisticsitems"] = sourceOrderObj?["flogisticsitems"] ?? "";
                orderData["fneedtransferorder"] = true;
            }
            //国补省市区、地址
            orderData["fprovince_gb"] = sourceOrderObj?["fprovince_gb"] ?? "";
            orderData["fcity_gb"] = sourceOrderObj?["fcity_gb"] ?? "";
            orderData["fregion_gb"] = sourceOrderObj?["fregion_gb"] ?? "";
            orderData["faddress_gb"] = sourceOrderObj?["faddress_gb"] ?? "";
            if (this.IsNotMgrInv)
            {
                // 勾选【不管理库存】
                // 收货人、手机号、省市区、详细地址 取采购订单源单（销售合同）上的终端客户信息
                orderData["fcustomercontactid"] = sourceOrderObj?["fcustomercontactid"] ?? "";
                orderData["fphone"] = sourceOrderObj?["fphone"] ?? "";
                orderData["fprovince"] = sourceOrderObj?["fprovince"] ?? "";
                orderData["fcity"] = sourceOrderObj?["fcity"] ?? "";
                orderData["fregion"] = sourceOrderObj?["fregion"] ?? "";
                orderData["faddress"] = sourceOrderObj?["faddress"] ?? "";
            }
            else
            {
                // 未勾选【不管理库存】
                // 收货人、手机号、省市区、详细地址 取二级分销商对应的客户信息
                orderData["fcustomercontactid"] = this.CurrentAgentCustomer?["fcustomercontactid"] ?? "";
                orderData["fphone"] = this.CurrentAgentCustomer?["fphone"] ?? "";
                orderData["fprovince"] = this.CurrentAgentCustomer?["fprovince"] ?? "";
                orderData["fcity"] = this.CurrentAgentCustomer?["fcity"] ?? "";
                orderData["fregion"] = this.CurrentAgentCustomer?["fregion"] ?? "";
                orderData["faddress"] = this.CurrentAgentCustomer?["faddress"] ?? "";
            }

            // 如果是二级分销商备货采购，生成的一级经销销售合同的收货人、手机号、省市区、详细地址 等取二级分销商本身对应的客户信息
            if (isStockUp)
            {
                orderData["fcustomercontactid"] = this.CurrentAgentCustomer?["fcustomercontactid"] ?? "";
                orderData["fphone"] = this.CurrentAgentCustomer?["fphone"] ?? "";
                orderData["fprovince"] = this.CurrentAgentCustomer?["fprovince"] ?? "";
                orderData["fcity"] = this.CurrentAgentCustomer?["fcity"] ?? "";
                orderData["fregion"] = this.CurrentAgentCustomer?["fregion"] ?? "";
                orderData["faddress"] = this.CurrentAgentCustomer?["faddress"] ?? "";
            }

            //携带二级分销的采购订单【采购员】(名称+手机号)到一级经销商的销售合同表头【跟单备注】
            DynamicObject poStaffInfo = purOrderData["fpostaffid_ref"] as DynamicObject;
            if (poStaffInfo != null)
            {
                var orderNoList = this.GetPurOrderEntryOrderNo(purOrderData);
                orderData["flogisticsitems"] = GetLogisticsItems(orderNoList,poStaffInfo);
            }


            // 终端客户
            orderData["fterminalcustomer"] = sourceOrderObj?["fcustomerid"] ?? "";
            orderData["fcontacts_c"] = sourceOrderObj?["fcontacter"] ?? "";
            orderData["fcoophone"] = sourceOrderObj?["fphone"] ?? "";
            orderData["fprovince_c"] = sourceOrderObj?["fprovince"] ?? "";
            orderData["fcity_c"] = sourceOrderObj?["fcity"] ?? "";
            orderData["fregion_c"] = sourceOrderObj?["fregion"] ?? "";
            orderData["fcooaddress"] = sourceOrderObj?["faddress"] ?? "";

            //todo 焕新订单字段特殊处理
            if (Convert.ToBoolean(purOrderData["frenewalflag"])) 
            {
                orderData["fmembershiptranid"] = Convert.ToString(purOrderData["fmembershiptranid"]);
                orderData["frenewalflag"] = Convert.ToString(purOrderData["frenewalflag"]);

            }

            // 构建销售合同明细数据包
            this.BuildOrderEntryData(orderData, purOrderData, productObjs, productPrices, hqPurPrices, sourceOrderObjReal?.ToList());

            // 构建销售合同销售员明细数据包
            this.BuildOrderDutyEntryData(orderData);

            return orderData;
        }

        //todo 未启用来源门店：销售部门取二级分销商部门关联门店对应的【来源门店】，需判断该门店是否属于一级经销商：如果为是，则取当前门店;如果为否，则需继续往上查找，直到查找到属于一级经销商的门店。

        /// <summary>
        /// 构建销售合同销售员明细数据包
        /// </summary>
        /// <param name="orderData">销售合同数据包</param>
        private void BuildOrderDutyEntryData(DynamicObject orderData)
        {
            var entrys = orderData["fdutyentry"] as DynamicObjectCollection;
            entrys.Clear();

            var entry = new DynamicObject(entrys.DynamicCollectionItemPropertyType);
            entry["fismain"] = true;
            entry["fdutyid"] = orderData["fstaffid"];
            entry["fdeptid"] = orderData["fdeptid"];
            entry["fratio"] = 100;
            entry["famount"] = orderData["fsumamount"];
            entry["fdescription"] = "";
            entrys.Add(entry);
        }

        /// <summary>
        /// 构建销售合同明细数据包
        /// </summary>
        /// <param name="orderData">销售合同数据包</param>
        /// <param name="purOrderData">采购订单数据包</param>
        /// <param name="productObjs">商品信息</param>
        /// <param name="productPrices">商品价格</param>
        /// <param name="hqPurPrices">商品总部采购价目</param>
        /// <param name="sourceOrderObj">采购订单上游销售合同信息</param>
        private void BuildOrderEntryData(
            DynamicObject orderData,
            DynamicObject purOrderData,
            DynamicObjectCollection productObjs,
            List<JObject> productPrices,
            List<JToken> hqPurPrices,
            List<DynamicObject> sourceOrderObj)
        {
            //是否焕新
            var frenewalflag = Convert.ToBoolean(purOrderData["frenewalflag"]);
            // 系统参数服务
            var profileService = this.Container.GetService<ISystemProfile>();

            // 销售合同赠品参与折扣计算
            var isgiftdiscount = profileService.GetSystemParameter(this.ParentAgentCtx, "bas_storesysparam", "fisgiftdiscount", false);

            // 非赠品商品的货品原值
            var faceAmountSum2 = 0M;

            var faceAmountSum = 0M;
            var distAmountSum = 0M;
            var dealAmountSum = 0M;
            var subsidyAmountSum = 0M;

            // 商品明细单据体
            // 合同商品明细的【零售价】取一级经销商定义的二级分销报价《销售价目》中的【统一零售价】
            var orderEntrys = orderData["fentry"] as DynamicObjectCollection;
            var purOrderEntrys = purOrderData["fentity"] as DynamicObjectCollection;

            // 将销售合同中存在但采购订单中不存在的明细行删除
            if (orderEntrys.Any() && purOrderEntrys.Any())
            {
                var beRemoves = new List<DynamicObject>();
                foreach (var orderEntry in orderEntrys)
                {
                    var purEntryId = Convert.ToString(orderEntry["fsourceentryid_e"]);
                    if (!purOrderEntrys.Any(o => Convert.ToString(o["id"]).EqualsIgnoreCase(purEntryId)))
                    {
                        beRemoves.Add(orderEntry);
                    }
                }
                foreach (var item in beRemoves)
                {
                    orderEntrys.Remove(item);
                }
            }
            bool needtransferorder = Convert.ToBoolean(purOrderData["fneedtransferorder"]);

            foreach (var purOrderEntry in purOrderEntrys)
            {
                var purEntryId = Convert.ToString(purOrderEntry["id"]);
                var purSourceEntryId = Convert.ToString(purOrderEntry["fsourceentryid_e"]);

                // 不存在则新增，已存在则覆盖
                var orderEntry = orderEntrys?.FirstOrDefault(o => Convert.ToString(o["fsourceentryid_e"]).EqualsIgnoreCase(purEntryId));
                if (orderEntry == null)
                {
                    orderEntry = new DynamicObject(orderEntrys.DynamicCollectionItemPropertyType);
                    orderEntrys.Add(orderEntry);
                    orderEntry["fclosestatus_e"] = CloseStatusConst.Default;
                }

                // 商品信息
                var productId = Convert.ToString(purOrderEntry["fmaterialid"]);
                var productObj = productObjs?.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(productId));

                // 商品统一零售价
                var productPrice = productPrices?.FirstOrDefault(o =>
                    Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(purEntryId)));

                // 总部采购价目，如果是总部零售价参数就取零售价
                var hqPurPrice = hqPurPrices?.FirstOrDefault(o =>
                    Convert.ToString(o["clientId"]).EqualsIgnoreCase(Convert.ToString(purEntryId)));

                //生成的一级分销销售合同【标准商品】的对应的零售价等于总部采购价目*二级分销档案的【分销价格系数】，其他的暂不处理，按之前逻辑走
                decimal price = 0;
                var hqprice = 0M;
                //if (this.pricerule.EqualsIgnoreCase("0"))
                //{
                //    hqprice = Convert.ToDecimal(hqPurPrice["purPrice"]);
                //}
                //else
                //{
                //    //还是以分销价目优先，取不到的话再取采购订单总部零售价为准
                //    if (Convert.ToDecimal(productPrice?["fsalprice"] ?? 0) > 0)
                //    {
                //        price = Convert.ToDecimal(productPrice?["fsalprice"] ?? 0);
                //    }
                //    else 
                //    {
                //        hqprice = Convert.ToDecimal(purOrderEntry?["fhqretailprice"] ?? 0);
                //    }
                //}
                ////
                //if (price==0)
                //{
                //    //0212 之前标准品才算系数，现在改成所有商品都以采购订单上的【总部零售价】* 系数，以此为基准。
                //    price = hqprice * matchResellRatio(Convert.ToString((purOrderEntry?["fmaterialid_ref"] as DynamicObject)["fseriesid"]), currentAgentInfo);
                //}
                var priceService = this.Container.GetService<IPriceService>();
                price = priceService.CalulateSubmitOnePirce(this.pricerule, Convert.ToDecimal(productPrice?["fsalprice"] ?? 0), Convert.ToDecimal(hqPurPrice?["purPrice"] ?? 0), purOrderEntry, currentAgentInfo);
                //if (!Convert.ToBoolean(purOrderEntry["funstdtype"]) && purOrderEntry["fattrinfo"].IsNullOrEmptyOrWhiteSpace() && productPrice.IsNullOrEmpty())
                //{
                //    price = hqprice * matchResellRatio(Convert.ToString((purOrderEntry?["fmaterialid_ref"] as DynamicObject)["fseriesid"]), currentAgentInfo);
                //}
                //else
                //{
                //    price = Convert.ToDecimal(productPrice?["fsalprice"] ?? 0);
                //}

                //todo 如果是焕新采购订单提交的一级销售合同，零售价取采购订单零售价
                if (Convert.ToBoolean(purOrderData["frenewalflag"]))
                {
                    price = Convert.ToDecimal(purOrderEntry?["fretailprice"] ?? 0);
                    var subsidyamount = Convert.ToDecimal(purOrderEntry["fsubsidyamount"]);
                    orderEntry["fsubsidyamount"] = subsidyamount;
                    subsidyAmountSum += subsidyamount;
                }
                var sourceOrderObjEntry = sourceOrderObj?.FirstOrDefault(o => !o["fentryid"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(o["fentryid"]) == purSourceEntryId);

                var bizQty = Convert.ToDecimal(purOrderEntry["fbizqty"]);
                var distRate = 10;
                var distRateRaw = 10;
                var amount = price * bizQty;
                var dealPrice = price * distRate / 10;
                //如果是焕新 成交单价取二级采购的 【零售成交单价】
                if (frenewalflag)
                {
                    dealPrice = Convert.ToDecimal(purOrderEntry["fretaildealprice"]);
                }
                var dealAmount = dealPrice * bizQty;
                var distAmount = amount - dealAmount;

                distAmountSum += distAmount; // 折扣额汇总
                dealAmountSum += dealAmount; // 成交金额汇总

                //69865 【内部提单】【241391】 【慕思现场-12.3.】二级订单提交一级后不应把赠品标记携带到一级合同
                //orderEntry["fisgiveaway"] = sourceOrderObjEntry?["fisgiveaway"] ?? false;
                // 货品原值汇总
                var isGiveaway = Convert.ToBoolean(orderEntry["fisgiveaway"]);
                if (isgiftdiscount)
                {
                    //汇总非赠品商品的货品原值
                    if (!isGiveaway)
                    {
                        faceAmountSum2 += amount;
                    }
                    faceAmountSum += amount;
                }
                else
                {
                    // 赠品不参与计算货品原值
                    if (!isGiveaway)
                    {
                        faceAmountSum += amount;
                    }
                }

                orderEntry["fproductid"] = productId;
                orderEntry["fmtrlimage"] = purOrderEntry["fmtrlimage"];
                orderEntry["fattrinfo"] = purOrderEntry["fattrinfo"];
                //【初始辅助属性】赋值
                orderEntry["fattrinfo_first"] = (purOrderEntry["fattrinfo_ref"] as DynamicObject)?["fname"].ToString();
                orderEntry["fcustomdes_e"] = purOrderEntry["fcustomdes_e"];
                orderEntry["funitid"] = purOrderEntry["funitid"];
                orderEntry["fbizunitid"] = productObj?["fsalunitid"] ?? ""; // 取商品的销售单位
                orderEntry["fsupplierid"] = productObj?["fsupplierid"] ?? ""; // 取商品的供应商
                orderEntry["funstdtype"] = purOrderEntry["funstdtype"];

                if (Convert.ToBoolean(purOrderEntry["funstdtype"]))
                {
                    ////'01':'新建','02':'待审批','03':'审批通过','04':'驳回','05':'待定价','06':'终审'
                    if (Convert.ToString(purOrderEntry["funstdtypestatus"]) != "01")
                    {
                        orderEntry["funstdtypestatus"] = purOrderEntry["funstdtypestatus"];
                        orderEntry["funstdtypecomment"] = purOrderEntry["funstdtypecomment"];
                    }
                }
                orderEntry["fqty"] = purOrderEntry["fqty"];
                orderEntry["fbizqty"] = bizQty;
                orderEntry["fprice"] = price;
                orderEntry["famount"] = amount;
                orderEntry["fdistrate"] = distRate;
                orderEntry["fdistrateraw"] = distRateRaw;
                orderEntry["fdealprice"] = dealPrice;
                orderEntry["fdealamount"] = dealAmount;
                orderEntry["fdistamount"] = distAmount;
                orderEntry["fresultbrandid"] = purOrderEntry["fresultbrandid"];
                orderEntry["fstockstatus"] = KCZT_SYS_01; // 库存状态默认为“可用”
                orderEntry["fdeliverymode"] = "0"; // 提货方式默认为“物流配送”
                orderEntry["fclosestatus_e"] = "0";
                orderEntry["fdescription"] = purOrderEntry["fnote"];
                //销售合同【未出库基本数量】=【销售数量】
                orderEntry["funstockoutqty"] = bizQty;

                //二级分销：把二级销售合同【成交单价】【成交金额】携带至一级销售合同【终端零售价】【终端金额】,【终端金额】根据数量计算
                var fterprice = sourceOrderObjEntry?["fdealprice"] ?? 0;
                orderEntry["fterprice"] = fterprice;
                orderEntry["fteramount"] = Convert.ToDecimal(fterprice) * bizQty;
                orderEntry["rounded_fdealprice"] = purOrderEntry["rounded_fdealprice"];
                orderEntry["rounded_fdealamount"] = purOrderEntry["rounded_fdealamount"];
                // 套件组合、沙发组合、配件 相关字段
                orderEntry["fsuitproductid"] = purOrderEntry["fsuitproductid"];
                orderEntry["fsuitdescription"] = purOrderEntry["fsuitdescription"];
                orderEntry["fsuitcombnumber"] = purOrderEntry["fsuitcombnumber"];
                orderEntry["fpartscombnumber"] = purOrderEntry["fpartscombnumber"];
                orderEntry["fsubqty"] = purOrderEntry["fsubqty"];
                orderEntry["fforproductid"] = purOrderEntry["fforproductid"];
                orderEntry["fforsuiteselectionid"] = purOrderEntry["fforsuiteselectionid"];
                orderEntry["description_suite"] = purOrderEntry["fdescription"];
                orderEntry["packagedescription"] = purOrderEntry["fpackagedescription"];
                orderEntry["fsofacombnumber"] = purOrderEntry["fsofacombnumber"];
                orderEntry["fpartqty"] = purOrderEntry["fpartqty"];
                orderEntry["fparttype"] = purOrderEntry["fparttype"];
                orderEntry["fiscombmain"] = purOrderEntry["fiscombmain"];
                orderEntry["fisautopartflag"] = purOrderEntry["fisautopartflag"];
                orderEntry["fprodrequirement"] = purOrderEntry["fprodrequirement"];
                orderEntry["fselsuiterequire"] = purOrderEntry["fselsuiterequire"];
                orderEntry["fdeptid"] = orderData["fdeptid"];

                //携带总部零售价
                orderEntry["fhqprice"] = sourceOrderObjEntry?["fhqprice"] ?? 0;
                //72047 从采购订单的总部零售价携带过去。
                var fhqretailprice = Convert.ToDecimal(purOrderEntry?["fhqretailprice"] ?? 0);
                if (fhqretailprice > 0)
                {
                    orderEntry["fhqprice"] = fhqretailprice;
                }
                //携带总部折扣率
                orderEntry["fhqdistrate"] = sourceOrderObjEntry?["fhqdistrate"] ?? 0;

                // 源单信息
                orderEntry["fsourcetype_e"] = this.HtmlForm.Id;
                orderEntry["fsourcenumber_e"] = purOrderData["fbillno"];
                orderEntry["fsourceentryid_e"] = purEntryId;

                if (needtransferorder)
                {
                    orderEntry["fdescription"] = sourceOrderObjEntry?["fdescription_e"] ?? string.Empty;
                }
            }

            // 财务信息
            orderData["ffaceamount"] = faceAmountSum;
            orderData["fdistamount"] = distAmountSum;
            orderData["fdealamount"] = dealAmountSum;
            orderData["fsubsidyamount"] = subsidyAmountSum;

            // 货款总折扣率
            var distSumRate = 0M;
            if (isgiftdiscount)
            {
                //货款总折扣率 = 成交金额 / 非赠品商品的货品原值
                distSumRate = faceAmountSum2 != 0 ? dealAmountSum / faceAmountSum2 : 1;
            }
            else
            {
                // 货款总折扣率 = 成交金额 / 货品原值
                distSumRate = faceAmountSum != 0 ? dealAmountSum / faceAmountSum : 1;
            }
            orderData["fdistsumrate"] = distSumRate;

            // 货款总折扣额 = 折扣额
            var distSumAmount = distAmountSum;
            orderData["fdistsumamount"] = distSumAmount;

            // 未收款 = 成交金额 + 费用收入 - 确认收款 - 申请退货金额
            var unReceived = dealAmountSum + 0 - Convert.ToDecimal(orderData["freceivable"]) - Convert.ToDecimal(orderData["frefundamount"]);
            orderData["funreceived"] = unReceived;

            // 订单总额 = 成交金额 + 费用收入
            var sumAmount = dealAmountSum + 0;
            orderData["fsumamount"] = sumAmount;

            if (frenewalflag)
            {
                //结算状态 默认“全款已收”。
                orderData["freceiptstatus"] = "receiptstatus_type_03";
                //未收款：0
                orderData["funreceived"] = 0;
                //确认收款：财务信息.成交金额。
                orderData["freceivable"] = orderData["fdealamount"];
                //结算进度: 已收款
                orderData["fsettlprogress"] = Enu_RenewalSettleProgress.已收款;
                //交货日期：合同交货日期
                orderData["fdeliverydate"] = purOrderData["fdeliverydate"];
                //会员商城支付日期：会员商城支付日期
                orderData["fmembershippaydate"] = purOrderData["fmembershippaydate"];
                //焕新订单类型：焕新订单类型
                orderData["frenewtype"] = purOrderData["frenewtype"];
                //合同附件：合同附件
                orderData["fimage"] = purOrderData["forderimage"];
            }
            //二级合同编号：取采购订单的源单编号
            orderData["fsecondorderno"] = purOrderData["fsourcenumber"];
            //合作渠道：取采购订单明细行任意一个合作渠道
            orderData["fchannel"] = purOrderEntrys?.Where(x=>!string.IsNullOrWhiteSpace(Convert.ToString(x["fchannel"]))).Select(x => x["fchannel"]).FirstOrDefault();
        }

        /// <summary>
        /// 保存采购订单
        /// </summary>
        /// <param name="purOrderDatas">采购订单数据包</param>
        /// <param name="successOrderDatas">生成成功的销售合同数据包</param>
        private void SavePurchaseOrders(DynamicObject[] purOrderDatas, List<DynamicObject> successOrderDatas)
        {
            // 当前需要反写的采购订单
            var _purOrderDatas = new List<DynamicObject>();

            foreach (var orderData in successOrderDatas)
            {
                var purchaseOrderId = Convert.ToString(orderData["fsourceid"]);
                var purOrderData = purOrderDatas.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(purchaseOrderId));

                // 反写【一级合同号】=销售合同号，【一级合同状态】=“提交至一级”
                purOrderData["fonelvoderno"] = orderData["fbillno"];
                purOrderData["fonelvoderstatus"] = OneLvOrderSratusConst.SubmitToHQ;
                if (purOrderData["fhqderdate"].IsNullOrEmptyOrWhiteSpace())
                {
                    //无值才更新，有值表示已提交过，日期不做更新
                    purOrderData["fhqderdate"] = DateTime.Now;
                }

                _purOrderDatas.Add(purOrderData);
            }

            if (!_purOrderDatas.Any()) return;

            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.HtmlForm.GetDynamicObjectType(this.Context));
            dm.Save(_purOrderDatas);

            foreach (var item in _purOrderDatas)
            {
                this.Result.ComplexMessage.SuccessMessages.Add(
                    $"{this.HtmlForm.Caption}【{item[this.HtmlForm.GetNumberField().PropertyName]}】{this.OperationName}成功！");
            }
        }

        /// <summary>
        /// 自动调用提交、审核一级<一键采购>功能。
        /// </summary>
        /// <param name="orderDatas"></param>
        private void AutoSubmitPur(List<DynamicObject> orderDatas) 
        {
            var OrderDatas_ReNew = orderDatas.Where(o => Convert.ToBoolean(o["frenewalflag"])).ToList();

            var submitEntities = OrderDatas_ReNew.Where(x => Convert.ToString(x["fstatus"]).EqualsIgnoreCase(BillStatus.B.ToString()) ||
                                                Convert.ToString(x["fstatus"]).EqualsIgnoreCase(BillStatus.C.ToString()))
                                    .ToList();
            if (submitEntities != null && submitEntities.Count > 0)
            {
                var result = this.Gateway.InvokeBillOperation(this.ParentAgentCtx, "ydj_order", submitEntities, "Submit", new Dictionary<string, object>
                    {
                        { "IgnoreCheckPermssion", true }
                    });
                result?.ThrowIfHasError(true, $"销售合同提交失败!");
            }
            var auditEntities = OrderDatas_ReNew.Where(x => Convert.ToString(x["fstatus"]).EqualsIgnoreCase(BillStatus.D.ToString())).ToList();
            if (auditEntities != null && auditEntities.Count > 0)
            {
                var result = this.Gateway.InvokeBillOperation(this.ParentAgentCtx, "ydj_order", auditEntities, "audit", new Dictionary<string, object>
                    {
                        { "IgnoreCheckPermssion", true }
                    });
                result?.ThrowIfHasError(true, $"销售合同审核失败!");

                result = this.Gateway.InvokeBillOperation(this.ParentAgentCtx, "ydj_order", auditEntities, "submitheadquart",
                            new Dictionary<string, object>
                            {
                        { "IgnoreCheckPermssion", true }, { "MSRenewalNotify", true }, { "SubmitAgent", true }
                            });
                result?.ThrowIfHasError(true, $"销售合同直营锁单失败!");

                var notPieceSendData = auditEntities.Where(a => !Convert.ToBoolean(a["fpiecesendtag"]));
                if (notPieceSendData.Any())
                {
                    result = this.Gateway.InvokeBillOperation(this.ParentAgentCtx, "ydj_order", notPieceSendData, "RenewalPur", new Dictionary<string, object>
                    {
                        { "IgnoreCheckPermssion", true }, { "MSRenewalNotify", true }, { "SubmitAgent", true }
                    });
                    result?.ThrowIfHasError(true, $"销售合同一键采购失败!");
                }
            }  
        }

        /// <summary>
        /// 保存销售合同
        /// </summary>
        /// <param name="orderDatas">销售合同数据包</param>
        /// <returns>生成成功的销售合同数据包</returns>
        private List<DynamicObject> SaveOrders(List<DynamicObject> orderDatas)
        {
            var successOrderDatas = new List<DynamicObject>();

            foreach (var orderData in orderDatas)
            {
                // 将销售合同的企业ID设置为一级经销商对应的企业ID，经销商ID其实就是企业ID
                orderData["fmainorgid"] = this.ParentAgentId;

                //保存时根据客户自动带出客户类型
                if (!orderData["fcustomerid"].IsNullOrEmptyOrWhiteSpace())
                {
                    var customerid = Convert.ToString(orderData["fcustomerid"]).Trim();
                    var customerObj = this.Context.LoadBizDataById("ydj_customer", customerid);
                    orderData["fcustomertype"] = customerObj["ftype"];
                }
            }

            var orderArray = orderDatas.ToArray();

            // 填充字段默认值
            var defCalulator = this.Container.GetService<IDefaultValueCalculator>();
            defCalulator.Execute(this.ParentAgentCtx, this.OrderForm, orderArray);

            // 保存前预处理
            var preService = this.Container.GetService<IPrepareSaveDataService>();
            preService.PrepareDataEntity(this.ParentAgentCtx, this.OrderForm, orderArray, OperateOption.Create());

            // 调用标准的暂存操作
            var saveResult = this.Gateway.InvokeBillOperation(
                this.ParentAgentCtx,
                this.OrderForm.Id,
                orderArray,
                "draft",
                new Dictionary<string, object>
                {
                    { "FromPurchaseOrderSubmitAgent", true }
                });
            saveResult.ThrowIfHasError(true, $"调用{this.OrderForm.Caption}暂存失败！");

            // 获取保存成功后的合同ID
            var orderIds = saveResult
                ?.SimpleData
                ?.GetValue("pkids")
                ?.FromJson<List<string>>() ?? new List<string>();

            foreach (var orderId in orderIds)
            {
                var orderData = orderDatas.FirstOrDefault(o => Convert.ToString(o["id"]).EqualsIgnoreCase(orderId));
                successOrderDatas.Add(orderData);
            }

            return successOrderDatas;
        }

        /// <summary>
        /// 加载当前当前二级经销商对应的客户ID
        /// </summary>
        private void LoadCurrentAgentCustomerId()
        {
            var sqlText = $@"
            select top 1 t1.fid,t1.fname,t1.fphone,t1.fprovince,t1.fcity,t1.fregion,t1.faddress,t1.fsrcstoreid,t1.fsource,t2.fcuscontacttryid fcustomercontactid 
            from t_ydj_customer t1 with(nolock) 
            left join t_ydj_fcuscontacttry t2 with(nolock) on t1.fid=t2.fid
            where forgid='{this.Context.Company}' 
            and fmainorgid ='{this.ParentAgentId}' AND t1.fforbidstatus=0 ";

            var customerObj = this.DBService
                .ExecuteDynamicObject(this.Context, sqlText)
                .FirstOrDefault();

            this.CurrentAgentCustomer = customerObj;
            this.CurrentAgentCustomerId = Convert.ToString(customerObj?["fid"]);
            this.SrcStoreId = Convert.ToString(customerObj?["fsrcstoreid"]);
        }

        /// <summary>
        /// 批量加载采购订单上游销售合同信息，如：消费者信息（客户），销售部门 等等
        /// </summary>
        /// <param name="purOrderDatas">采购订单数据包</param>
        /// <returns>采购订单上游销售合同信息</returns>
        private List<DynamicObject> LoadSourceOrder(DynamicObject[] purOrderDatas)
        {
            var sourceOrderObjs = new List<DynamicObject>();

            List<string> orderNos = new List<string>();
            foreach (var item in purOrderDatas)
            {
                var entry = item["fentity"] as DynamicObjectCollection;
                orderNos.AddRange(entry.Select(a => Convert.ToString(a["fsourceentryid_e"])).ToList());
            }
            orderNos = orderNos.Distinct().ToList();
            //var orderNos = purOrderDatas
            //    .Select(o => Convert.ToString(o["forderno"]))
            //    .Distinct()
            //    .ToList();

            if (!orderNos.Any()) return sourceOrderObjs;

            var dbService = this.Container.GetService<IDBService>();

            using (var tran = Context.CreateTransaction())
            {
                var sqlText = "";
                var tempTable = "";
                if (orderNos.IsGreaterThan(50))
                {
                    //用临时表关联查询
                    tempTable = dbService.CreateTempTableWithDataList(this.Context, orderNos, false);
                    sqlText = $@"
                select o.flogisticsitems,o.fdescription,d.fdescription fdescription_e,o.fbillno forderno,o.fcustomercontactid,c.fcontacter,o.fcustomerid,o.fphone,o.flinkstaffid,o.fprovince,o.fcity,o.fregion,o.faddress,o.fprovince_gb,o.fcity_gb,o.fregion_gb,o.faddress_gb,o.fdeptid,d.fentryid,d.fdealprice,d.fdealamount,d.fisgiveaway,d.fhqprice,d.fhqdistrate from t_ydj_order o with(nolock)
                inner join t_ydj_orderentry d with(nolock) on o.fid=d.fid
                inner join {tempTable} tt with(nolock) on tt.fid = d.fentryid
                inner join t_ydj_fcuscontacttry c with(nolock) on o.fcustomercontactid=c.fcuscontacttryid
                where o.fmainorgid='{this.Context.Company}'";
                }
                else if (orderNos.Count == 1)
                {
                    sqlText = $@"
                select o.flogisticsitems,o.fdescription,d.fdescription fdescription_e,o.fbillno forderno,o.fcustomercontactid,c.fcontacter,o.fcustomerid,o.fphone,o.flinkstaffid,o.fprovince,o.fcity,o.fregion,o.faddress,o.fprovince_gb,o.fcity_gb,o.fregion_gb,o.faddress_gb,o.fdeptid,d.fentryid,d.fdealprice,d.fdealamount,d.fisgiveaway,d.fhqprice,d.fhqdistrate from t_ydj_order o with(nolock)
                inner join t_ydj_orderentry d with(nolock) on o.fid=d.fid
                inner join t_ydj_fcuscontacttry c with(nolock) on o.fcustomercontactid=c.fcuscontacttryid
                where o.fmainorgid='{this.Context.Company}' and d.fentryid='{orderNos[0]}'";
                }
                else
                {
                    sqlText = $@"
                select o.flogisticsitems,o.fdescription,d.fdescription fdescription_e,o.fbillno forderno,o.fcustomercontactid,c.fcontacter,o.fcustomerid,o.fphone,o.flinkstaffid,o.fprovince,o.fcity,o.fregion,o.faddress,o.fprovince_gb,o.fcity_gb,o.fregion_gb,o.faddress_gb,o.fdeptid,d.fentryid,d.fdealprice,d.fdealamount,d.fisgiveaway,d.fhqprice,d.fhqdistrate from t_ydj_order o with(nolock)
                inner join t_ydj_orderentry d with(nolock) on o.fid=d.fid
                inner join t_ydj_fcuscontacttry c with(nolock) on o.fcustomercontactid=c.fcuscontacttryid
                where o.fmainorgid='{this.Context.Company}' and d.fentryid in('{string.Join("','", orderNos)}')";
                }

                sourceOrderObjs = dbService
                    .ExecuteDynamicObject(this.Context, sqlText)
                    .OfType<DynamicObject>()
                    .ToList();

                tran.Complete();

                dbService.DeleteTempTableByName(this.Context, tempTable, true);

                return sourceOrderObjs;
            }
        }

        /// <summary>
        /// 批量加载采购订单已提交过的一级经销商销售合同数据包
        /// </summary>
        /// <param name="purOrderDatas">采购订单数据包</param>
        /// <returns>采购订单已提交过的一级经销商销售合同数据包</returns>
        private List<DynamicObject> LoadAgentOrders(DynamicObject[] purOrderDatas)
        {
            var agentOrderObjs = new List<DynamicObject>();

            // 此处可以做下业务优化：只查【驳回】的采购订单ID。
            // 1、因为只有驳回的或未提交的采购订单才能被提交到一级经销商。
            // 2、如果是未提交的采购订单，那一定是没有生成过一级经销商的销售合同，所以此处也无需关联查询一级经销商的销售合同。
            //var purOrderIds = purOrderDatas
            //    .Where(o => Convert.ToString(o["fonelvoderstatus"]).EqualsIgnoreCase(OneLvOrderSratusConst.Reject))
            //    .Select(o => Convert.ToString(o["id"]))
            //    .ToList();

            var purOrderIds = purOrderDatas
                .Select(o => Convert.ToString(o["id"]))
                .ToList();

            if (!purOrderIds.Any()) return agentOrderObjs;

            var dbService = this.Container.GetService<IDBService>();
            List<string> agentOrderIds;
            using (var tran = Context.CreateTransaction())
            {
                var sqlText = "";
                var tempTable = "";
                if (purOrderIds.IsGreaterThan(50))
                {
                    //用临时表关联查询
                    tempTable = dbService.CreateTempTableWithDataList(this.Context, purOrderIds, false);
                    sqlText = $@"
                select o.fid from t_ydj_order o with(nolock)
                inner join {tempTable} tt with(nolock) on tt.fid = o.fsourceid";
                }
                else if (purOrderIds.Count == 1)
                {
                    sqlText = $@"
                select fid from t_ydj_order o with(nolock)
                where fsourceid='{purOrderIds[0]}'";
                }
                else
                {
                    sqlText = $@"
                select fid from t_ydj_order o with(nolock)
                where fsourceid in('{string.Join("','", purOrderIds)}')";
                }

                agentOrderIds = dbService
                    .ExecuteDynamicObject(this.Context, sqlText)
                    .Select(o => Convert.ToString(o["fid"]))
                    .ToList();

                tran.Complete();

                dbService.DeleteTempTableByName(this.Context, tempTable, true);
            }

            if (!agentOrderIds.Any()) return agentOrderObjs;

            // 根据ID加载一级经销商的销售合同数据包
            var dm = this.GetDataManager();
            dm.InitDbContext(this.Context, this.OrderForm.GetDynamicObjectType(this.Context));
            agentOrderObjs = dm.Select(agentOrderIds)?.OfType<DynamicObject>()?.ToList();

            return agentOrderObjs;
        }

        /// <summary>
        /// 批量加载采购订单商品信息
        /// </summary>
        /// <param name="purOrderDatas">采购订单数据包</param>
        /// <returns>商品信息</returns>
        private DynamicObjectCollection LoadProducts(DynamicObject[] purOrderDatas)
        {
            // 所有的商品ID
            var productIds = purOrderDatas.SelectMany(o =>
            {
                var entrys = o["fentity"] as DynamicObjectCollection;
                var _productIds = entrys
                    .Select(entry => Convert.ToString(entry["fmaterialid"]))
                    .Where(productId => !productId.IsNullOrEmptyOrWhiteSpace());
                return _productIds;
            })
            .Distinct().ToList();

            if (!productIds.Any()) return null;

            // 批量加载商品信息
            var productObjs = this.Context.LoadBizBillHeadDataById("ydj_product", productIds, "fsupplierid,fsalunitid");

            return productObjs;
        }

        /// <summary>
        /// 批量加载一级经销商定义的商品二级分销价格
        /// </summary>
        /// <param name="purOrderDatas">采购订单数据包</param>
        /// <returns>商品二级分销价格</returns>
        private List<JObject> LoadProductResellerPrice(DynamicObject[] purOrderDatas)
        {
            // 取价参数
            var productInfos = new JArray();

            foreach (var purOrderData in purOrderDatas)
            {
                var purOrderEntrys = purOrderData["fentity"] as DynamicObjectCollection;
                foreach (var purOrderEntry in purOrderEntrys)
                {
                    productInfos.Add(new JObject
                    {
                        ["clientId"] = purOrderEntry["id"] as string,
                        ["productId"] = purOrderEntry["fmaterialid"] as string,
                        ["bizDate"] = DateTime.Now,
                        ["customerId"] = this.CurrentAgentCustomerId,
                        ["attrInfo"] = new JObject
                        {
                            ["id"] = purOrderEntry["fattrinfo"] as string,
                            ["entities"] = new JArray()
                        }
                    });
                }
            }

            var priceService = this.Container.GetService<IPriceService>();
            var priceList = priceService.GetResellerPrice(this.ParentAgentCtx, productInfos);

            return priceList;
        }
        /// <summary>
        /// 递归获取一级经销商的部门
        /// </summary>
        /// <param name="userCtx"></param>
        /// <param name="subDeptid">二级经销商的部门（关联门店可能被多次转让）</param>
        /// <returns></returns>
        public List<DynamicObject> LoadTopAgentDept(List<string> subAgentDeptIds)
        {
            var agentDepts = new List<DynamicObject>();

            subAgentDeptIds = subAgentDeptIds.Distinct().ToList();

            if (!subAgentDeptIds.Any()) return agentDepts;

            //优先获取一级视角下专属的二级分销部门。取不到再按原逻辑找。
            agentDepts = LoadTopAgentDeptBySub(subAgentDeptIds);
            if (agentDepts.Any()) return agentDepts;  

            var dbService = this.Container.GetService<IDBService>();

            var sqlText = "";
            var tempTable = "";
            using (var tran = Context.CreateTransaction())
            {
                if (subAgentDeptIds.IsGreaterThan(1))
                {
                    //用临时表关联查询
                    tempTable = dbService.CreateTempTableWithDataList(this.Context, subAgentDeptIds, false);

                    sqlText = $@"/*dialect*/
                            --获取指定上级所有来源门店的数据
                            with temp(fsubagentdeptid,fagentdeptid,fsrcstoreid,fnumber,orgid,fmainorgid,fagentid) as
                            (
                            --一级经销商的门店，没有来源门店字段
                            select tt.fid as fsubagentdeptid,dept.fid as fagentdeptid,store1.fsrcstoreid,store1.fnumber,store1.fmainorgid as orgid,dept.fmainorgid,store1.fagentid
                            from t_bd_department as dept 
                            inner join t_bas_store as store1 on store1.fid=dept.fstore
                            inner join {tempTable} tt with(nolock) on tt.fid=dept.fid 
                            where dept.fforbidstatus='0' 
                            union all
                            select temp.fsubagentdeptid,dept.fid as fagentdeptid,store1.fsrcstoreid,store1.fnumber,store1.fmainorgid as orgid,dept.fmainorgid,store1.fagentid from
                            t_bd_department as dept 
                            inner join t_bas_store as store1 on store1.fid=dept.fstore
                            inner join temp on temp.fsrcstoreid = store1.fid 
                            where dept.fforbidstatus='0' 
                            )
                            --找到一级经销商的门店对应部门
                            select distinct fsubagentdeptid,fagentdeptid from temp
                            where fsrcstoreid ='' and fmainorgid = fagentid ";
                }
                else if (subAgentDeptIds.Count == 1)
                {
                    sqlText = $@"/*dialect*/
                            --获取指定上级所有来源门店的数据
                            with temp(fsubagentdeptid,fagentdeptid,fsrcstoreid,fnumber,orgid,fmainorgid,fagentid ) as
                            (
                            --一级经销商的门店，没有来源门店字段
                            select '{subAgentDeptIds[0]}' as fsubagentdeptid,dept.fid as fagentdeptid,store1.fsrcstoreid,store1.fnumber,store1.fmainorgid as orgid,dept.fmainorgid,store1.fagentid 
                            from
                            t_bd_department as dept inner join t_bas_store as store1 on store1.fid=dept.fstore
                            where dept.fid ='{subAgentDeptIds[0]}' and dept.fforbidstatus='0'
                            union all
                            select temp.fsubagentdeptid,dept.fid as fagentdeptid,store1.fsrcstoreid,store1.fnumber,store1.fmainorgid as orgid,dept.fmainorgid,store1.fagentid  from
                            t_bd_department as dept 
                            inner join t_bas_store as store1 on store1.fid=dept.fstore
                            inner join temp on temp.fsrcstoreid = store1.fid 
                            where dept.fforbidstatus='0' 
                            )
                            --找到一级经销商的门店对应部门
                            select distinct fsubagentdeptid,fagentdeptid from temp
                            where fsrcstoreid ='' and fmainorgid = fagentid ";
                }

                agentDepts = dbService
                    .ExecuteDynamicObject(this.Context, sqlText)
                    .OfType<DynamicObject>()
                    .ToList();

                tran.Complete();

                dbService.DeleteTempTableByName(this.Context, tempTable, true);

                return agentDepts;
            }

        }

        /// <summary>
        /// 优先获取一级视角下专属的二级分销部门。取不到再按原逻辑找。
        /// </summary>
        /// <param name="subAgentDeptIds"></param>
        /// <returns></returns>
        private List<DynamicObject> LoadTopAgentDeptBySub(List<string> subAgentDeptIds) 
        {
            var agentDepts = new List<DynamicObject>();

            subAgentDeptIds = subAgentDeptIds.Distinct().ToList();

            if (!subAgentDeptIds.Any()) return agentDepts;

            var dbService = this.Container.GetService<IDBService>();
            var sqlText = ""; 
            var tempTable = "";
            using (var tran = Context.CreateTransaction())
            {
                if (subAgentDeptIds.IsGreaterThan(1))
                {
                    //用临时表关联查询
                    tempTable = dbService.CreateTempTableWithDataList(this.Context, subAgentDeptIds, false);

                    sqlText = $@"/*dialect*/     
                        --获取指定上级所有来源门店的数据
                        with temp(fsubagentdeptid,fagentdeptid,fsrcstoreid,fnumber,orgid,fmainorgid,fagentid,fsubagentid) as
                        (
                        --一级经销商的门店，没有来源门店字段
                        select tt.fid as fsubagentdeptid,dept.fid as fagentdeptid,store1.fsrcstoreid,store1.fnumber,store1.fmainorgid as orgid,dept.fmainorgid,store1.fagentid,dept.fsubagentid 
                        from
                        t_bd_department as dept inner join t_bas_store as store1 on store1.fid=dept.fstore
                        inner join {tempTable} tt with(nolock) on tt.fid=dept.fid 
                        where dept.fforbidstatus='0'
                        union all
                        select temp.fsubagentdeptid,dept.fid as fagentdeptid,store1.fsrcstoreid,store1.fnumber,store1.fmainorgid as orgid,dept.fmainorgid,store1.fagentid,dept.fsubagentid from
                        t_bd_department as dept 
                        inner join t_bas_store as store1 on store1.fid=dept.fstore
                        inner join temp on temp.fsrcstoreid = store1.fid 
                        where dept.fforbidstatus='0' 
                        )
                        --找到一级经销商的门店对应部门
                        select distinct fsubagentdeptid,fagentdeptid,fsubagentid from temp
                        inner join {tempTable} tt with(nolock) on tt.fid=temp.fsubagentid 
                        where fsrcstoreid =''
                        --and fmainorgid = fagentid
                        and fsubagentid ='{this.Context.Company}'";
                }
                else if (subAgentDeptIds.Count == 1)
                {
                    sqlText = $@"/*dialect*/     
                        --获取指定上级所有来源门店的数据
                        with temp(fsubagentdeptid,fagentdeptid,fsrcstoreid,fnumber,orgid,fmainorgid,fagentid,fsubagentid) as
                        (
                        --一级经销商的门店，没有来源门店字段
                        select '{subAgentDeptIds[0]}' as fsubagentdeptid,dept.fid as fagentdeptid,store1.fsrcstoreid,store1.fnumber,store1.fmainorgid as orgid,dept.fmainorgid,store1.fagentid,dept.fsubagentid 
                        from
                        t_bd_department as dept inner join t_bas_store as store1 on store1.fid=dept.fstore
                        where dept.fid ='{subAgentDeptIds[0]}' and dept.fforbidstatus='0'
                        union all
                        select temp.fsubagentdeptid,dept.fid as fagentdeptid,store1.fsrcstoreid,store1.fnumber,store1.fmainorgid as orgid,dept.fmainorgid,store1.fagentid,dept.fsubagentid  from
                        t_bd_department as dept 
                        inner join t_bas_store as store1 on store1.fid=dept.fstore
                        inner join temp on temp.fsrcstoreid = store1.fid 
                        where dept.fforbidstatus='0' 
                        )
                        --找到一级经销商的门店对应部门
                        select distinct fsubagentdeptid,fagentdeptid,fsubagentid from temp
                        where fsrcstoreid =''
                        --and fmainorgid = fagentid
                        and fsubagentid ='{this.Context.Company}' ";
                }

                agentDepts = dbService
                .ExecuteDynamicObject(this.Context, sqlText)
                .OfType<DynamicObject>()
                .ToList();

                tran.Complete();

                dbService.DeleteTempTableByName(this.Context, tempTable, true);

                return agentDepts;
            }
        }

        /// <summary>
        /// 批量加载二级分销商的部门所关联的门店的来源门店的部门
        /// 只考虑一层，没有考虑超级转让可能存在多层的情况。现在需要调整一直往上找 直到找到一级经销商下的部门为止
        /// </summary>
        /// <param name="subAgentDeptIds">二级分销商的部门ID</param>
        /// <returns>部门所关联的门店的来源门店的部门</returns>
        private List<DynamicObject> LoadAgentDepts(List<string> subAgentDeptIds)
        {
            var agentDepts = new List<DynamicObject>();

            subAgentDeptIds = subAgentDeptIds.Distinct().ToList();

            if (!subAgentDeptIds.Any()) return agentDepts;

            var dbService = this.Container.GetService<IDBService>();

            var sqlText = "";
            var tempTable = "";
            using (var tran = Context.CreateTransaction())
            {
                if (subAgentDeptIds.IsGreaterThan(50))
                {
                    //用临时表关联查询
                    tempTable = dbService.CreateTempTableWithDataList(this.Context, subAgentDeptIds, false);
                    sqlText = $@"
                select distinct t3.fid fsubagentdeptid,t1.fid fagentdeptid from t_bd_department t1 with(nolock)
                inner join t_bas_store t2 with(nolock) on t2.fsrcstoreid=t1.fstore and t2.fsrcstoreid!=''
                inner join t_bd_department t3 with(nolock) on t3.fstore=t2.fid 
                inner join {tempTable} tt with(nolock) on tt.fid=t3.fid";
                }
                else if (subAgentDeptIds.Count == 1)
                {
                    sqlText = $@"
                select distinct t3.fid fsubagentdeptid,t1.fid fagentdeptid from t_bd_department t1 with(nolock)
                inner join t_bas_store t2 with(nolock) on t2.fsrcstoreid=t1.fstore and t2.fsrcstoreid!=''
                inner join t_bd_department t3 with(nolock) on t3.fstore=t2.fid 
                where t3.fid='{subAgentDeptIds[0]}'";
                }
                else
                {
                    sqlText = $@"
                select distinct t3.fid fsubagentdeptid,t1.fid fagentdeptid from t_bd_department t1 with(nolock)
                inner join t_bas_store t2 with(nolock) on t2.fsrcstoreid=t1.fstore and t2.fsrcstoreid!=''
                inner join t_bd_department t3 with(nolock) on t3.fstore=t2.fid 
                where t3.fid in('{string.Join("','", subAgentDeptIds)}')";
                }

                agentDepts = dbService
                    .ExecuteDynamicObject(this.Context, sqlText)
                    .OfType<DynamicObject>()
                    .ToList();

                tran.Complete();

                dbService.DeleteTempTableByName(this.Context, tempTable, true);

                return agentDepts;
            }
        }

        /// <summary>
        /// 批量加载商品总部采购价格
        /// </summary>
        /// <param name="purOrderDatas"></param>
        /// <returns></returns>
        private List<JToken> LoadProductStandPrice(DynamicObject[] purOrderDatas)
        {
            // 取价参数
            var productInfos = new JArray();

            foreach (var purOrderData in purOrderDatas)
            {
                var purOrderEntrys = purOrderData["fentity"] as DynamicObjectCollection;
                //这里暂时只获取标准品的，后续要开放其他可对应处理
                foreach (var purOrderEntry in purOrderEntrys.Where(x => !Convert.ToBoolean(x["funstdtype"]) && x["fattrinfo"].IsNullOrEmptyOrWhiteSpace())) 
                {
                    productInfos.Add(new JObject
                    {
                        ["clientId"] = purOrderEntry["id"] as string,
                        ["productId"] = purOrderEntry["fmaterialid"] as string,
                        ["bizDate"] = DateTime.Now,
                        //["supplierId"] = purOrderData["fsupplierid"] as string,
                        ["customerId"] = this.CurrentAgentCustomerId,
                        ["attrInfo"] = new JObject
                        {
                            ["id"] = purOrderEntry["fattrinfo"] as string,
                            ["entities"] = new JArray()
                        },
                        ["isHqPrice"] = true,
                        ["isReHqPurPrice"] = true,
                        ["supplierNotFilter"] = true//直接取最新采购价，忽略供应商取最新总部价目
                    });
                }
            }

            //var postData = new Dictionary<string, object>
            //    {
            //        { "priceFlag",4},
            //        { "modeType",1},
            //        { "idType","localDataId"},
            //        { "hopCount",0},
            //        { "productInfos",productInfos}
            //    };
            List<JToken> srvData = new List<JToken>();
            if (productInfos.Any())
            {
                ////获取总部供应商，后取总部价格
                //var hqSupplier = this.Context.ExecuteDynamicObject($"select top 1 fid from T_YDJ_SUPPLIER where fmainorgid='{Context.TopCompanyId}'",null)?.FirstOrDefault()["fid"];
                //foreach (var item in productInfos)
                //{
                //    item["supplierId"] = Convert.ToString(hqSupplier);
                //}

                IPriceService priceService = this.Container.GetService<IPriceService>();
                //srvData = priceService.GetPrice(this.Context, 4, productInfos);
                //todo 根据经销商参数【二级分销合同价格计算规则】，如果是二级分销合同按总部出厂价计算,则走原逻辑取取《采购价目》的【统一零售价】；如果是二级分销合同按总部零售价计算,则取《销售价目》的【统一零售价】
                var AgentData = this.Context.LoadBizBillHeadDataById("bas_agent", this.Context.Company, "fpricerule");
                this.pricerule = Convert.ToString(AgentData?["fpricerule"] ?? "0");
                //'0':'二级分销合同按总部出厂价计算','1':'二级分销合同按总部零售价计算'
                if (this.pricerule.EqualsIgnoreCase("0"))
                {
                    //采购价目
                    srvData = priceService.GetPrice(this.Context, 4, productInfos);
                }
                else
                {
                    //销售价目
                    srvData = priceService.GetPrice(this.Context, 1, productInfos);
                }
                //var result = this.Gateway.InvokeBillOperation(this.Context, "ydj_purchaseprice", purOrderDatas, "getprices", postData);
                //result.ThrowIfHasError(true, "获取价目失败!");

            }
            return srvData;
        }

        /// <summary>
        /// 根据系列及二级分销商信息匹配销售系数
        /// </summary>
        /// <param name="fseriesid">系列id</param>
        /// <param name="currAgentInfo">二级分销商信息</param>
        /// <returns></returns>
        public decimal matchResellRatio(string fseriesid, DynamicObjectCollection currAgentInfo)
        {
            decimal resellRatio = 1;

            if (currAgentInfo == null || !currAgentInfo.Any())
            {
                return resellRatio;
            }
            //拿系列匹配分销价格系数明细配置，匹配到则取对应系数，匹配不到取表头的系数计算
            var matchRatio = currAgentInfo.FirstOrDefault(x => !x["fseriesid"].IsNullOrEmptyOrWhiteSpace() && Convert.ToString(x["fseriesid"]) == fseriesid);
            if (matchRatio == null)
            {
                var ratio = Convert.ToDecimal(currAgentInfo.First()["fresellratio"]);
                return ratio <= 0 ? 1 : ratio;
            }
            return Convert.ToDecimal(matchRatio["fresellratio_e"]);
        }

        /// <summary>
        /// 获取源订单信息
        /// </summary>
        /// <param name="purOrderDatas"></param>
        public void GetSourceOrderList(DynamicObject[] purOrderDatas)
        {
            if (sourceOrderObjs == null)
            {
                sourceOrderObjs = new List<DynamicObject>();
            }

            if (sourceOrderObjs != null && sourceOrderObjs.Any() )
            {
                sourceOrderObjs.Clear();
            }

            var entitys = purOrderDatas.SelectMany(x=>x["fentity"] as DynamicObjectCollection).ToList();
            var sourceNumberList = new List<string>();
            if (entitys != null && entitys.Any())
            {
                sourceNumberList  = entitys.Where(x => Convert.ToString(x["fsourceformid"]).Equals("ydj_order") && !Convert.ToString(x["fsourcebillno"]).IsNullOrEmptyOrWhiteSpace())
                                            .Select(y => Convert.ToString(y["fsourcebillno"])).ToList();
                if (sourceNumberList != null && sourceNumberList.Any())
                {
                    sourceNumberList = sourceNumberList.Distinct().ToList();
                }
            }
            //sourceNumberList = purOrderDatas.Where(x=>!Convert.ToString(x["fsourcenumber"]).IsNullOrEmptyOrWhiteSpace()).Select(x=>Convert.ToString(x["fsourcenumber"])).ToList();

            if (sourceNumberList != null && sourceNumberList.Any())
            {
                var distinctOrderNumberList = sourceNumberList.Distinct().ToList();

                var sqlStr = $" fbillno in ({String.Join(",",distinctOrderNumberList.Select(x=>$"'{x}'"))}) and fmainorgid = '{this.Context.Company}' ";

                sourceOrderObjs = this.Context.LoadBizBillHeadDataByACLFilter("ydj_order",sqlStr, "fbillno,fstaffid");

                var orderStaffIdList = sourceOrderObjs.Where(x=>!Convert.ToString(x["fstaffid"]).IsNullOrEmptyOrWhiteSpace()).Select(x=>Convert.ToString(x["fstaffid"])).ToList();

                if (orderStaffIdList != null && orderStaffIdList.Any())
                {
                    if (orderInfoDic == null)
                    {
                        orderInfoDic = new Dictionary<string, List<OrderStaffInfo>>();
                    }

                    var distinctOrderStaffIdList = orderStaffIdList.Distinct().ToList();

                    var loadStaffDys = this.Context.LoadBizBillHeadDataById("ydj_staff", distinctOrderStaffIdList, "fnumber,fname,fphone");

                    foreach (var orderObj in sourceOrderObjs)
                    {
                        var staffId = Convert.ToString(orderObj["fstaffid"]);

                        var orderNo = Convert.ToString(orderObj["fbillno"]);

                        if (!staffId.IsNullOrEmptyOrWhiteSpace())
                        {
                            var findStaffDy = loadStaffDys.FirstOrDefault(x=>Convert.ToString(x["id"]).Equals(staffId));
                            if (findStaffDy != null)
                            {
                                var orderStaffInfos = new List<OrderStaffInfo>();

                                orderStaffInfos.Add(new OrderStaffInfo()
                                {
                                    StaffId = Convert.ToString(findStaffDy["id"]),
                                    StaffName = Convert.ToString(findStaffDy["fname"]),
                                    StaffPhone = Convert.ToString(findStaffDy["fphone"])
                                });

                                if (!orderInfoDic.ContainsKey(orderNo))
                                {
                                    orderInfoDic.Add(orderNo, orderStaffInfos);
                                }
                                
                            }
                        }

                    }
                }
            }
        }

        /// <summary>
        /// 构建跟单备注数据包
        /// </summary>
        /// <param name="orderNoList">销售合同订单号</param>
        /// <param name="purchaseOrderStaff">采购订单中的采购人员的数据包</param>
        /// <returns></returns>
        public string GetLogisticsItems(List<string> orderNoList,DynamicObject purchaseOrderStaff)
        {
            //"采购员：" + Convert.ToString(poStaffInfo["fname"]) + "，电话：" + Convert.ToString(poStaffInfo["fphone"])
            var logisticsItemBuilder = new StringBuilder();
            logisticsItemBuilder.Append($"[采购员：{Convert.ToString(purchaseOrderStaff["fname"])},电话：{Convert.ToString(Convert.ToString(purchaseOrderStaff["fphone"]))}]");
            if (orderInfoDic != null && orderInfoDic.Any())
            {
                var dicResult = new Dictionary<string,string>();
                if (dicResult != null && dicResult.Any())
                {
                    dicResult.Clear();
                }
                foreach (var orderNo in orderNoList)
                {
                    if (orderInfoDic.ContainsKey(orderNo))
                    {
                        var orderStaffInfos = orderInfoDic[orderNo];
                        foreach (var orderStaffInfo in orderStaffInfos)
                        {
                            if (dicResult.ContainsKey(orderStaffInfo.StaffId))
                            {
                                continue;
                            }
                            logisticsItemBuilder.Append($"[销售员：{orderStaffInfo.StaffName},电话：{orderStaffInfo.StaffPhone}]");
                            //这里是做去重，如果有添加的话，那就不需要再次加入
                            dicResult.Add(orderStaffInfo.StaffId,orderStaffInfo.StaffId);
                        }
                    }
                }
            }
            return logisticsItemBuilder.ToString();
        }

        /// <summary>
        /// 获取采购订单明细行里的销售合同明细
        /// </summary>
        /// <param name="purchaseOrderDy"></param>
        /// <returns></returns>
        private List<string> GetPurOrderEntryOrderNo(DynamicObject purchaseOrderDy)
        {
            var purProductEntrys = purchaseOrderDy["fentity"] as DynamicObjectCollection;
            var orderNoList = new List<string>();
            if (purProductEntrys != null && purProductEntrys.Any())
            {
                orderNoList = purProductEntrys.Where(x => Convert.ToString(x["fsourceformid"]).Equals("ydj_order") &&
                                                         !Convert.ToString(x["fsourcebillno"]).IsNullOrEmptyOrWhiteSpace())
                                .Select(x => Convert.ToString(x["fsourcebillno"]))
                                .ToList();
                if (orderNoList != null && orderNoList.Any())
                {
                    orderNoList = orderNoList.Distinct().ToList();
                }
            }
            
            return orderNoList;
        }
    }

    public class OrderStaffInfo
    {
        /// <summary>
        /// 员工ID
        /// </summary>
        public string StaffId { set; get; }

        /// <summary>
        /// 员工名称
        /// </summary>
        public string StaffName { set; get; }

        /// <summary>
        /// 员工电话
        /// </summary>
        public string StaffPhone { set; get; }
    }
}
