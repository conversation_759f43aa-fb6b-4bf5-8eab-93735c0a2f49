using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using JieNor.Framework;
using JieNor.Framework.IoC;
using JieNor.Framework.Meta.Designer;
using JieNor.Framework.Interface.BizTask;
using System.Threading;
using JieNor.Framework.Interface.QueryBuilder;
using JieNor.AMS.YDJ.Store.AppService.Service.Archiving;

namespace JieNor.AMS.YDJ.Store.AppService.SchedulerTask
{
    /// <summary>
    /// 预留单自动归档
    /// </summary>
    [InjectService]
    [TaskSvrId("AutoArchiving")]
    [Caption("预留单自动归档（在总部执行）")]
    //[TaskMultiInstance()]
    [Browsable(false)]
    public class ReserveAutoArchiving : AbstractScheduleWorker
    {
        /// <summary>
        /// 执行任务逻辑
        /// </summary>
        /// <returns></returns>
        protected override async Task DoExecute()
        { 
            var ctx = this.UserContext; 
            try
            {
                var archivingService = ctx.Container.GetService<ArchivingService>();
                var orgStrSql = $@"select fmainorgid from t_stk_reservebill with(nolock) group by fmainorgid ";
                var allGrpOrgs = this.DBService.ExecuteDynamicObject(ctx, orgStrSql);

                var grpOrgs = new List<string>();
                grpOrgs = allGrpOrgs.Select(x => Convert.ToString(x["fmainorgid"])).ToList();
                foreach (var item in grpOrgs)
                {
                    var fmainorgid = Convert.ToString(item);
                    if (!fmainorgid.IsNullOrEmptyOrWhiteSpace())
                    {
                        var ctxX = ctx.CreateAgentDBContext(fmainorgid);
                        try
                        {
                            var result = archivingService?.Archiving_reserve(ctxX, this.Option); 
                            this.WriteLog("经销商 {0} ：{1}".Fmt(ctxX.Company, result.SimpleMessage));
                        }
                        catch (Exception e)
                        {
                            this.WriteLog("经销商{0}归档异常 ：{1}".Fmt(ctxX.Company, e.Message));
                        }
                    }
                }
            }
            catch (Exception e)
            {
                this.WriteLog("预留单自动归档异常！" + e.Message);
            }
        }
    }
}
