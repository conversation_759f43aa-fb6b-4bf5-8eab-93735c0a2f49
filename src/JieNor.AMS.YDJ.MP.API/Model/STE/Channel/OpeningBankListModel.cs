using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Policy;
using System.Text;
using System.Threading.Tasks;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MP.API.Model
{
    public class OpeningBankListModel : BaseDataSimpleModel
    {
        public string Bankname { get; set; } = string.Empty;
        public OpeningBankListModel()
        {
       
        }

        public OpeningBankListModel(DynamicObject data) : base(data)
        { 

        }
    }
}
