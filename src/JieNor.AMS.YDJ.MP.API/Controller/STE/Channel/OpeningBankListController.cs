using System;
using System.Collections.Generic;
using System.Text;
using JieNor.Framework;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.AMS.YDJ.Core.Interface;
using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Order
{
    /// <summary>
    /// 微信小程序：开户银行列表取数接口
    /// </summary>
    public class OpeningBankListController : BaseController
    {
        public HtmlForm OrderForm;
        protected HtmlForm ProductForm { get; set; }
        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(OpeningBankListDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<BaseListPageData<OpeningBankListModel>>
            {
                Data = new BaseListPageData<OpeningBankListModel>(dto.PageSize)
            }; 

            resp.Message = "操作成功！";
            resp.Success = true;
            resp.Data.TotalRecord = GetTotalRecord(dto);
            resp.Data.List = MapTo(dto);

            return resp;
        }

        private int GetTotalRecord(OpeningBankListDTO dto)
        {
            //查询总记录数
            var totalRecord = 0;

            List<SqlParam> sqlParams = new List<SqlParam>
            {
               // new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
            };

            var sqlWhere = new StringBuilder();
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                sqlWhere.Append(" and (fname like @keyword)");
                sqlParams.Add(new SqlParam("@keyword", System.Data.DbType.String, $"%{dto.Keyword}%"));
            }

            string sql =
                $@"select count(1) as totalRecord from t_ydj_openingbank with(nolock)
                where 1=1 {sqlWhere} ";
            using (var reader = this.DBService.ExecuteReader(this.Context, sql, sqlParams))
            {
                if (reader.Read())
                {
                    totalRecord = Convert.ToInt32(reader["totalRecord"]);
                }
            }

            return totalRecord;
        }

        private List<OpeningBankListModel> MapTo(OpeningBankListDTO dto)
        {
            int pageIndex = dto.PageIndex < 1 ? 1 : dto.PageIndex;
            var pageSize = dto.PageSize < 1 ? 10 : dto.PageSize;

            //默认按创建日期降序
            var orderBy = "fname desc";

            var sqlParams = new List<SqlParam>
            {
                //new SqlParam("@fmainorgid", System.Data.DbType.String, this.Context.Company)
            };

            var sqlWhere = new StringBuilder();
            if (!dto.Keyword.IsNullOrEmptyOrWhiteSpace())
            {
                sqlWhere.Append(" and (fname like @keyword or fnumber like @keyword )");
                sqlParams.Add(new SqlParam("@keyword", System.Data.DbType.String, $"%{dto.Keyword}%"));
            }

            //查询分页数据
            var sqlText = $@"
            select top {pageSize} * from 
            (
	            select row_number() over(order by {orderBy}) rownum,
	                    fid,fnumber,fname,fbankname
                    from t_ydj_openingbank with(nolock)
                where 1=1 {sqlWhere} 
            ) p 
            where rownum > {pageSize} * ({pageIndex} - 1)";

            var list = this.DBService.ExecuteDynamicObject(this.Context, sqlText, sqlParams);

            var models = new List<OpeningBankListModel>();
            foreach (var item in list)
            {
                models.Add(new OpeningBankListModel
                {
                    Id = JNConvert.ToStringAndTrim(item["fid"]),
                    Number = JNConvert.ToStringAndTrim(item["fnumber"]),
                    Name = JNConvert.ToStringAndTrim(item["fname"]),
                    Bankname = JNConvert.ToStringAndTrim(item["fbankname"]),
                });
            }

            return models;
        }
    }
}