using JieNor.AMS.YDJ.MP.API.DTO;
using JieNor.AMS.YDJ.MP.API.Model;
using JieNor.AMS.YDJ.MP.API.Utils;
using JieNor.Framework;
using JieNor.Framework.Interface;
using JieNor.Framework.MetaCore.FormMeta;
using JieNor.Framework.SuperOrm.DataEntity;
using System;
using System.Collections.Generic;
using System.Linq;

namespace JieNor.AMS.YDJ.MP.API.Controller.STE.Channel
{
    /// <summary>
    /// 微信小程序：合作渠道编辑取数接口
    /// </summary>
    public class ChannelEditController : BaseController
    {
        /// <summary>
        /// 楼盘表单模型
        /// </summary>
        protected HtmlForm ChannelForm { get; set; }

        /// <summary>
        /// 处理请求
        /// </summary>
        /// <param name="dto"></param>
        /// <returns></returns>
        public object Any(ChannelEditDTO dto)
        {
            base.InitializeOperationContext(dto);

            var resp = new BaseResponse<ChannelEditModel>();

            this.ChannelForm = this.MetaModelService.LoadFormModel(this.Context, "ste_channel");

            if (!dto.Id.IsNullOrEmptyOrWhiteSpace())
            {
                var channelObj = this.ChannelForm.GetBizDataById(this.Context, dto.Id, true);

                if (channelObj == null)
                {
                    resp.Message = "渠道伙伴不存在或已被删除，不允许操作！";
                    resp.Success = false;
                    return resp;
                }

                resp.Data.Id = JNConvert.ToStringAndTrim(channelObj["id"]);
                resp.Data.Number = JNConvert.ToStringAndTrim(channelObj["fnumber"]);
                resp.Data.Name = JNConvert.ToStringAndTrim(channelObj["fname"]);
                resp.Data.CreateDate = Convert.ToDateTime(channelObj["fcreatedate"]);

                resp.Data.Province = new ComboDataModel(channelObj["fprovince_ref"] as DynamicObject);
                resp.Data.City = new ComboDataModel(channelObj["fcity_ref"] as DynamicObject);
                resp.Data.Region = new ComboDataModel(channelObj["fregion_ref"] as DynamicObject);
                resp.Data.District = this.ChannelForm.GetDistrictText(this.Context, channelObj);
                resp.Data.Address = JNConvert.ToStringAndTrim(channelObj["faddress"]);

                resp.Data.Description = JNConvert.ToStringAndTrim(channelObj["fdescription"]);
                resp.Data.Company = JNConvert.ToStringAndTrim(channelObj["fcompany"]);
                resp.Data.Phone = JNConvert.ToStringAndTrim(channelObj["fphone"]);
                resp.Data.Cooperation = new ComboDataModel(channelObj["fcooperation_ref"] as DynamicObject);
                resp.Data.Type = new ComboDataModel(channelObj["ftype_ref"] as DynamicObject);
                resp.Data.SumBillAmount = Convert.ToDecimal(channelObj["fsumbillamount"]);

                // 收款信息
                resp.Data.AccountName = JNConvert.ToStringAndTrim(channelObj["faccountname"]);
                resp.Data.Bank = JNConvert.ToStringAndTrim(channelObj["fbank"]);
                resp.Data.BankNumber = JNConvert.ToStringAndTrim(channelObj["fbanknumber"]);
                resp.Data.BankCode = new ComboDataModel(channelObj["fbankcode_ref"] as DynamicObject); 
                resp.Data.Idcard = JNConvert.ToStringAndTrim(channelObj["fidcard"]);
                resp.Data.Suppliercode = JNConvert.ToStringAndTrim(channelObj["fsuppliercode"]);
                resp.Data.ChannelType = JNConvert.ToStringAndTrim(channelObj["fchanneltype"]);

                //证件
                if (!JNConvert.ToStringAndTrim(channelObj["fmulimage"]).IsNullOrEmptyOrWhiteSpace())
                {
                    resp.Data.MulImages = ImageFieldUtil.ParseImages(channelObj, "fmulimage",true);
                }
                // 合作门店、负责人、联系人
                //resp.Data.Dept = new BaseDataSimpleModel(channelObj["fdeptid_ref"] as DynamicObject);
                //resp.Data.Staff = new BaseDataSimpleModel(channelObj["fstaffid_ref"] as DynamicObject);
                resp.Data.Contact = JNConvert.ToStringAndTrim(channelObj["fcontacts"]);
                
                #region 设置抽佣比例

                SetChannelEntryInfo(channelObj,resp);

                #endregion
            }
            else
            {
                // 默认值
                //var currStaff = this.Context.GetCurrentStaff();
                //resp.Data.Staff = new BaseDataSimpleModel
                //{
                //    Id = currStaff.Id,
                //    Name = currStaff.Name,
                //    Number = currStaff.Number
                //};
                //var currDept = this.Context.GetCurrentDept();
                //resp.Data.Dept = new BaseDataSimpleModel
                //{
                //    Id = currDept.Id,
                //    Name = currDept.Name,
                //    Number = currDept.Number
                //};
            }

            // 获取合作渠道相关的基础资料
            // 查询所有门店
            if (dto.SrcFormId.IsNullOrEmptyOrWhiteSpace())
            {
                dto.SrcFormId = this.ChannelForm.Id;
            }
            resp.Data.ComboData = this.Context.GetDeptDataSource(dto);
            resp.Data.ComboData.Merge(this.ChannelForm.GetComboDataSource(this.Context, "ftype,fcooperation"));

            resp.Message = "操作成功！";
            resp.Success = true;

            return resp;
        }

        /// <summary>
        /// 设置抽佣比例信息
        /// </summary>
        /// <param name="channelObj">合作渠道数据包</param>
        /// <param name="resp">返回的响应对象</param>
        public void SetChannelEntryInfo(DynamicObject channelObj, BaseResponse<ChannelEditModel> resp)
        {
            if (resp.Data.ChannelEntryList == null)
            {
                resp.Data.ChannelEntryList = new List<ChannelEntry>();
            }
            
            var channelEntrys = channelObj["fentry"] as DynamicObjectCollection;
            if (channelEntrys != null && channelEntrys.Any())
            {
                foreach (var item in channelEntrys)
                {
                    resp.Data.ChannelEntryList.Add(new ChannelEntry
                    {
                        Id = JNConvert.ToStringAndTrim(item["id"]),
                        LowerLimitAmount = Convert.ToDecimal(item["flowerlimit"]),
                        Ratio = Convert.ToDecimal(item["fratio"]),
                        UpperLimitAmount = Convert.ToDecimal(item["fupperlimit"])
                    });
                }
            }
            
        }
    }
}