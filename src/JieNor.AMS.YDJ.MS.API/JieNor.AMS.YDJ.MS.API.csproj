<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{ABE7BA67-D93B-5A87-E6B8-D6D4DD03601A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>JieNor.AMS.YDJ.MS.API</RootNamespace>
    <AssemblyName>JieNor.AMS.YDJ.MS.API</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\lib\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Autofac, Version=4.6.0.0, Culture=neutral, PublicKeyToken=17863af14b0044da, processorArchitecture=MSIL">
      <HintPath>..\packages\Autofac.4.6.0\lib\net45\Autofac.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.AMS.YDJ.Core, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.AMS.YDJ.Core.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.AMS.YDJ.DataTransferObject, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.AMS.YDJ.DataTransferObject.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.DataEntity, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.DataEntity.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.DataTransferObject, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.DataTransferObject.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.Interface, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.Interface.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.MetaCore, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.MetaCore.dll</HintPath>
    </Reference>
    <Reference Include="JieNor.Framework.SuperOrm, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\JieNor.Framework.SuperOrm.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Api.Swagger, Version=0.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Api.Swagger.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Client, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Client.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Common, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Common.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Interfaces, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Interfaces.dll</HintPath>
    </Reference>
    <Reference Include="ServiceStack.Text, Version=4.6.617.938, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Debug\ServiceStack.Text.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Controller\Agent\AgentChangeLicenseController.cs" />
    <Compile Include="Controller\BaseAuthController.cs" />
    <Compile Include="Controller\BaseController.cs" />
    <Compile Include="Controller\BaseNotAuthController.cs" />
    <Compile Include="Controller\Category\CategoryPushController.cs" />
    <Compile Include="Controller\CostCenter\CostCenterController.cs" />
    <Compile Include="Controller\Customer\PushCustomerSapNumberController.cs" />
    <Compile Include="Controller\DeliveryOrder\CreatePurchaseInOrderController.cs" />
    <Compile Include="Controller\Dept\DeptForbidController.cs" />
    <Compile Include="Controller\Dept\DeptListController.cs" />
    <Compile Include="Controller\Dept\DeptSyncController.cs" />
    <Compile Include="Controller\Engineer\DelEngineerController.cs" />
    <Compile Include="Controller\Engineer\PushEngineerController.cs" />
    <Compile Include="Controller\EnumData\EnumDataListController.cs" />
    <Compile Include="Controller\HeadStatement\HeadStatementCancelController.cs" />
    <Compile Include="Controller\HeadStatement\HeadStatementSyncController.cs" />
    <Compile Include="Controller\IncomeDisburse\IncomeDisburseExamineController.cs" />
    <Compile Include="Controller\InventoryVerify\BarCodeReturnProductInfoController.cs" />
    <Compile Include="Controller\LogisticStatement\LogisticStatementCancelController.cs" />
    <Compile Include="Controller\LogisticStatement\LogisticStatementSyncController.cs" />
    <Compile Include="Controller\MarketStatement\UpdateMarketStatementController.cs" />
    <Compile Include="Controller\OpeningBank\OpeningBankController.cs" />
    <Compile Include="Controller\Order\DirectOrder\PushDirectOrderShareCostBillController.cs" />
    <Compile Include="Controller\Order\DirectOrder\ReturnDirectOrderHeadquarterStatusController.cs" />
    <Compile Include="Controller\Order\OrderFileController.cs" />
    <Compile Include="Controller\Order\OrderProcessController.cs" />
    <Compile Include="Controller\Order\OrderRenewalNotifyController.cs" />
    <Compile Include="Controller\Order\OrderRecRefundController.cs" />
    <Compile Include="Controller\Order\OrderReNewCancelController.cs" />
    <Compile Include="Controller\Order\OrderUnstdTypeAuditController.cs" />
    <Compile Include="Controller\OtherStockOut\OtherStockOutController.cs" />
    <Compile Include="Controller\PDA\GetPDAVersionController.cs" />
    <Compile Include="Controller\Position\PositionForbidController.cs" />
    <Compile Include="Controller\Position\PositionSyncController.cs" />
    <Compile Include="Controller\ProductAuth\ProductAuthController.cs" />
    <Compile Include="Controller\ProductDelisting\ProductDelistingPushController.cs" />
    <Compile Include="Controller\ProductDelisting\ProductDelistingQueryController.cs" />
    <Compile Include="Controller\ProductPack\ProductPackPushController.cs" />
    <Compile Include="Controller\Product\GoodListController.cs" />
    <Compile Include="Controller\PromotCombine\PromotCombineController.cs" />
    <Compile Include="Controller\PurchaseOrder\PurchaseOrderPushController.cs" />
    <Compile Include="Controller\PurchaseOrder\PurchaseOrderChgSaveController.cs" />
    <Compile Include="Controller\PurchaseOrder\PurchaseOrderReNewCancelController.cs" />
    <Compile Include="Controller\PurchaseOrder\PurchaseOrderSaveController.cs" />
    <Compile Include="Controller\PurchaseOrder\PurchaseOrderStockWaterAuditController.cs" />
    <Compile Include="Controller\PurchaseOrder\PurchaseOrderUnstdTypeAuditController.cs" />
    <Compile Include="Controller\PurchasePrice\PurchasePricePushController.cs" />
    <Compile Include="Controller\Registfee\UpdateRegistfeeController.cs" />
    <Compile Include="Controller\Role\RoleForbidController.cs" />
    <Compile Include="Controller\Role\RoleSyncController.cs" />
    <Compile Include="Controller\SelFittingsMap\SelFittingsMapPushController.cs" />
    <Compile Include="Controller\SelUnstdtype\SelUnstdtypePushController.cs" />
    <Compile Include="Controller\SelSuit\SelSuitPushController.cs" />
    <Compile Include="Controller\SelType\SelTypePushController.cs" />
    <Compile Include="Controller\Service\DelServiceController.cs" />
    <Compile Include="Controller\Service\PushServiceController.cs" />
    <Compile Include="Controller\Staff\StaffForbidController.cs" />
    <Compile Include="Controller\Staff\StaffSyncController.cs" />
    <Compile Include="Controller\Ste\AfterFeedback\DelFeedBackController.cs" />
    <Compile Include="Controller\Ste\AfterFeedback\PushFeedBackController.cs" />
    <Compile Include="Controller\Ste\Customerrecord\DelCustomerrecordController.cs" />
    <Compile Include="Controller\Ste\Customerrecord\PushCustomerrecordController.cs" />
    <Compile Include="Controller\Ste\Customerrecord\RecycleCustomerrecordController.cs" />
    <Compile Include="Controller\Ste\Customer\PushCustomerController.cs" />
    <Compile Include="Controller\Ste\Customer\PushMemberController.cs" />
    <Compile Include="Controller\Ste\Followerrecord\PushFollowerrecordController.cs" />
    <Compile Include="Controller\STK\Inventoryverify\InventoryPushController.cs" />
    <Compile Include="Controller\Supplier\CreateSupplierController.cs" />
    <Compile Include="Controller\TransferOrderApply\TransferOrderApplyAuditController.cs" />
    <Compile Include="Controller\TransferOrderApply\TransferOrderApplyRejectController.cs" />
    <Compile Include="Controller\User\UnBindQyWxController.cs" />
    <Compile Include="Controller\User\ChangePwdController.cs" />
    <Compile Include="Controller\User\CreateTokenController.cs" />
    <Compile Include="Controller\User\UserForbidController.cs" />
    <Compile Include="Controller\User\UserSyncBatchController.cs" />
    <Compile Include="Controller\User\UserSyncAlterController.cs" />
    <Compile Include="Controller\User\UserSyncController.cs" />
    <Compile Include="Controller\Vist\DelVistController.cs" />
    <Compile Include="Controller\Vist\PushVistController.cs" />
    <Compile Include="DTO\Agent\AgentChangeLicenseDto.cs" />
    <Compile Include="DTO\AuxPropValDTO.cs" />
    <Compile Include="DTO\BaseDTO.cs" />
    <Compile Include="DTO\Category\CategoryPushDTO.cs" />
    <Compile Include="DTO\CostCenter\CostCenterDTO.cs" />
    <Compile Include="DTO\Customer\PushCustomerSapNumberDTO.cs" />
    <Compile Include="DTO\DeliveryOrder\DeliveryOrderDTO.cs" />
    <Compile Include="DTO\Dept\DeptDeleteDto.cs" />
    <Compile Include="DTO\Dept\DeptForbidDto.cs" />
    <Compile Include="DTO\Dept\DeptListDto.cs" />
    <Compile Include="DTO\Dept\DeptSyncDto.cs" />
    <Compile Include="DTO\Engineer\DelEngineerDTO.cs" />
    <Compile Include="DTO\Engineer\PushEngineerDTO.cs" />
    <Compile Include="DTO\EnumData\EnumDataListDTO.cs" />
    <Compile Include="DTO\HeadStatement\HeadStatementCancelDTO.cs" />
    <Compile Include="DTO\HeadStatement\HeadStatementSyncDTO.cs" />
    <Compile Include="DTO\IncomeDisburse\IncomeDisburseDto.cs" />
    <Compile Include="DTO\InventoryVerify\BarCodeReturnProductInfoDTO.cs" />
    <Compile Include="DTO\LogisticStatement\LogisticStatementCancelDTO.cs" />
    <Compile Include="DTO\LogisticStatement\LogisticStatementSyncDTO.cs" />
    <Compile Include="DTO\MarketStatement\UpdateMarketStatementDto.cs" />
    <Compile Include="DTO\OpeningBank\OpeningBankDTO.cs" />
    <Compile Include="DTO\Order\DirectOrder\PushDirectOrderShareCostBillNoDTO.cs" />
    <Compile Include="DTO\Order\DirectOrder\ReturnDirectOrderHeadquarterStatusDTO.cs" />
    <Compile Include="DTO\Order\OrderFileDTO.cs" />
    <Compile Include="DTO\Order\OrderProcessDTO.cs" />
    <Compile Include="DTO\Order\OrderRenewalNotifyDTO.cs" />
    <Compile Include="DTO\Order\OrderReNewCancelDTO.cs" />
    <Compile Include="DTO\Order\OrderReNewCancelNotifyDTO.cs" />
    <Compile Include="DTO\Order\OrderUnstdTypeAuditDTO.cs" />
    <Compile Include="DTO\OtherStockOut\OtherStockOutAuditDTO.cs" />
    <Compile Include="DTO\PDA\GetPDAVersionDto.cs" />
    <Compile Include="DTO\Position\PositionForbidDto.cs" />
    <Compile Include="DTO\Position\PositionSyncDto.cs" />
    <Compile Include="DTO\ProductAuth\ProductAuthDTO.cs" />
    <Compile Include="DTO\ProductDelisting\ProductDelistingBase.cs" />
    <Compile Include="DTO\ProductDelisting\ProductDelistingDataDTO.cs" />
    <Compile Include="DTO\ProductDelisting\ProductDelistingPushDTO.cs" />
    <Compile Include="DTO\ProductDelisting\ProductDelistingQueryDTO.cs" />
    <Compile Include="DTO\ProductPack\ProductPackPushDTO.cs" />
    <Compile Include="DTO\Product\GoodListDTO.cs" />
    <Compile Include="DTO\PromotCombine\PromotCombineDTO.cs" />
    <Compile Include="DTO\PurchaseOrder\Enu_DiscountType.cs" />
    <Compile Include="DTO\PurchaseOrder\PurchaseOrderPushDTO.cs" />
    <Compile Include="DTO\PurchaseOrder\PurchaseOrderChgSaveDTO.cs" />
    <Compile Include="DTO\PurchaseOrder\PurchaseOrderReNewCancelDTO.cs" />
    <Compile Include="DTO\PurchaseOrder\PurchaseOrderSaveDTO.cs" />
    <Compile Include="DTO\PurchaseOrder\PurchaseOrderStockWaterAuditDTO.cs" />
    <Compile Include="DTO\PurchaseOrder\PurchaseOrderUnstdTypeAuditDTO.cs" />
    <Compile Include="DTO\PurchasePrice\PurchasePricePushDTO.cs" />
    <Compile Include="DTO\Registfee\UpdateRegistfeeDto.cs" />
    <Compile Include="DTO\Role\RoleForbidDto.cs" />
    <Compile Include="DTO\Role\RoleSyncDto.cs" />
    <Compile Include="DTO\SelFittingsMap\SelFittingsMapPushDTO.cs" />
    <Compile Include="DTO\SelUnstdtype\SelUnstdtypePushDTO.cs" />
    <Compile Include="DTO\SelSuit\SelSuitPushDTO.cs" />
    <Compile Include="DTO\SelType\SelTypePushDTO.cs" />
    <Compile Include="DTO\Service\DelServiceDTO.cs" />
    <Compile Include="DTO\Service\PushServiceDTO.cs" />
    <Compile Include="DTO\Staff\StaffForbidDto.cs" />
    <Compile Include="DTO\Staff\StaffSyncDto.cs" />
    <Compile Include="DTO\Ste\AfterFeedback\DelFeedBackDTO.cs" />
    <Compile Include="DTO\Ste\AfterFeedback\PushFeedBackDTO.cs" />
    <Compile Include="DTO\Ste\Customerrecord\DelCustomerrecordDTO.cs" />
    <Compile Include="DTO\Ste\Customerrecord\PushCustomerrecordDTO.cs" />
    <Compile Include="DTO\Ste\Customerrecord\RecycleCustomerrecordDTO.cs" />
    <Compile Include="DTO\Ste\Customer\PushCustomerDTO.cs" />
    <Compile Include="DTO\Ste\Customer\PushMemberDTO.cs" />
    <Compile Include="DTO\Ste\Followerrecord\PushFollowerrecordDTO.cs" />
    <Compile Include="DTO\STK\Inventoryverify\InventoryPushDTO.cs" />
    <Compile Include="DTO\Supplier\CreateSupplierDto.cs" />
    <Compile Include="DTO\TransferOrderApply\TransferOrderApplyAuditDTO.cs" />
    <Compile Include="DTO\TransferOrderApply\TransferOrderApplyRejectDTO.cs" />
    <Compile Include="DTO\User\UnBindQyWxDto.cs" />
    <Compile Include="DTO\User\ChangePwdDto.cs" />
    <Compile Include="DTO\User\CreateTokenDto.cs" />
    <Compile Include="DTO\User\UserForbidDto.cs" />
    <Compile Include="DTO\User\UserSyncBatchDto.cs" />
    <Compile Include="DTO\User\UserSyncAlterDto.cs" />
    <Compile Include="DTO\User\UserSyncDto.cs" />
    <Compile Include="DTO\Vist\DelVistDTO.cs" />
    <Compile Include="DTO\Vist\PushVistDTO.cs" />
    <Compile Include="Filter\BaseResponseFilter.cs" />
    <Compile Include="Filter\BaseRequestFilter.cs" />
    <Compile Include="Filter\OperationLogFilter.cs" />
    <Compile Include="Filter\RepeatedRequestFilter.cs" />
    <Compile Include="Model\MuSiData.cs" />
    <Compile Include="Plugin\AsyncOperationLog\GetLogRequestContent.cs" />
    <Compile Include="Plugin\AsyncOperationLog\ManualProcess.cs" />
    <Compile Include="Plugin\AsyncOperationLog\Retry.cs" />
    <Compile Include="Plugin\Base\AsyncOperationLogService.cs" />
    <Compile Include="Plugin\BeforeMapFieldEventArgs.cs" />
    <Compile Include="Plugin\HeadStatement\MSAdjust.cs" />
    <Compile Include="Plugin\HeadStatement\MSCancel.cs" />
    <Compile Include="Plugin\HeadStatement\MSSave.cs" />
    <Compile Include="Plugin\LogisticStatement\MSCancel.cs" />
    <Compile Include="Plugin\LogisticStatement\MSSave.cs" />
    <Compile Include="Plugin\Order\MSRenewalNotify.cs" />
    <Compile Include="Plugin\PurchaseOrderChg\MSSave.cs" />
    <Compile Include="Plugin\PurchaseOrder\MSSave.cs" />
    <Compile Include="Plugin\PurchaseOrder\MSUnstdTypeAudit.cs" />
    <Compile Include="Plugin\Order\MSUnstdTypeAudit.cs" />
    <Compile Include="Plugin\TransferOrderApply\MSReject.cs" />
    <Compile Include="Plugin\TransferOrderApply\MSAudit.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RegisterController.cs" />
    <Compile Include="Response\BaseListData.cs" />
    <Compile Include="Response\BaseListPageData.cs" />
    <Compile Include="Response\BaseResponse.cs" />
    <Compile Include="SchedulerTask\MSApiTask.cs" />
    <Compile Include="Utils\MusiAuthHelper.cs" />
    <Compile Include="Utils\HtmlFormExtentions.cs" />
    <Compile Include="Utils\MSKey.cs" />
    <Compile Include="Utils\ProductUtil.cs" />
    <Compile Include="Utils\PurchaseOrderUtil.cs" />
    <Compile Include="Utils\ServiceStackExtentions.cs" />
    <Compile Include="Utils\StreamUtility.cs" />
    <Compile Include="Utils\UserContextExtentions.cs" />
    <Compile Include="Utils\IOperationResultExtentions.cs" />
    <Compile Include="Validation\MusiAuthValidation.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Readme.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\JieNor.AMS.YDJ.SWJ.API\JieNor.AMS.YDJ.SWJ.API.csproj">
      <Project>{065761D1-29F7-491D-ABF0-30A8D7872226}</Project>
      <Name>JieNor.AMS.YDJ.SWJ.API</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- <PropertyGroup>
    <PostBuildEvent>xcopy /e /r /y "$(TargetDir)$(TargetName).dll" "$(SolutionDir)JieNor.AMS.YDJ.Store.Web\bin\"
xcopy /e /r /y "$(TargetDir)$(TargetName).pdb" "$(SolutionDir)JieNor.AMS.YDJ.Store.Web\bin\"</PostBuildEvent>
  </PropertyGroup> -->
</Project>