<!doctype html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_selfpurchaseprice" basemodel="ydj_purchaseprice" el="3" cn="自建采购价目" isolate="1" IsAsynLstDesc="true" querydbnode="">
 
    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <!--<div id="opList">
        <ul el="10" id="save" op="save" opn="保存">
            <li el="11" vid="510" ek="fentry" cn="采购价必须大于0" data="{'expr':'fpurprice>0 ','message':'采购价必须大于0！'}"></li>
        </ul>
        <ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fname','fnumber','fproductid_e','fpurprice','fstartdate_e','fexpiredate_e', 'fconfirmstatus']}" permid=""></ul>
        <ul el="10" id="confirm" op="confirm" opn="确认" data="" permid="ydj_purchaseprice_confirm"></ul>
        <ul el="10" id="cancelconfirm" op="cancelconfirm" opn="取消确认" data="" permid="ydj_purchaseprice_cancelconfirm"></ul>
    </div>-->

    <!--表单所涉及的权限项定义-->
    <!--<div id="permList">
        <ul el="12" id="ydj_purchaseprice_confirm" cn="确认"></ul>
        <ul el="12" id="ydj_purchaseprice_cancelconfirm" cn="取消确认"></ul>
    </div>-->
</body>
</html>
