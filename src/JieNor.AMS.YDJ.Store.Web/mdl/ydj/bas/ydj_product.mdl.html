<!--本模板是根据服务端需求提炼而成，客户端页面只需要遵循下述节点的一些属性描述规范即可，
    至于节点布局，样式等没有要求（也就是xpath没要求），可由客户端页面实际需求自由发挥，
    但需要服务端保存的节点需遵守下述规范：基本就是增加几个自定义属性（id,fn,tn）
    -->
<html lang="en">
<head>
</head>
<body id="ydj_product" basemodel="bd_material" el="3" fqfks="fspecifica" cn="商品" isolate="0" dac="true" ludt="List" bftfks="fnumber,fname,fspecifica,fcategoryid,fspace,fseriesid,fstyle,fbrandid" IsAsynLstDesc="true" querydbnode="">
	<div id="fbillhead" el="51" pk="fid" pn="fbillhead" cn="商品">

		<!--重写基类模型中的部分字段属性-->
		<input id="fnumber" el="108" cn="编码" copy="0" lix="1" must="1" />
		<input id="fname" el="100" len="200" sbm="true" must="1" />
		<input id="fdescription" el="100" len="500" sbm="true" visible="1150" />
		<input id="fdataorigin" el="100" cn="数据来源" visible="0" />
		<input id="fprimitiveid" el="100" cn="原始数据ID" remove />

		<input group="基本信息" el="135" id="fimage" ek="fbillhead" fn="fimage" pn="fimage" cn="商品主图" uploadser="hwobs" visible="-1" lix="5" />
		<input group="基本信息" el="106" id="fcategoryid" ek="fbillhead" fn="fcategoryid" refid="ydj_category" ts="" visible="-1" sbm="true" cn="商品类别" lix="10" must="1" />
		<input group="基本信息" el="106" ek="fbillhead" id="fseltypeid" fn="fseltypeid" pn="fseltypeid" cn="型号" refid="sel_type" ts="" copy="0" visible="-1" lix="15" />
		<input group="基本信息" el="106" id="fbrandid" ek="fbillhead" fn="fbrandid" ts="" visible="-1" cn="品牌" refid="ydj_brand" lix="20" sbm="true" />
		<input group="基本信息" el="131" id="fauxseriesid" ek="fbillhead" fn="fauxseriesid" ts="" visible="-1" cn="附属品牌" refid="ydj_series"  lix="23" sbm="true" />
		<input group="基本信息" el="106" id="fseriesid" ek="fbillhead" fn="fseriesid" ts="" visible="-1" cn="系列" refid="ydj_series" lix="25" sbm="true" />
		<input group="基本信息" el="106" id="fsubseriesid" ek="fbillhead" fn="fsubseriesid" ts="" visible="1150" cn="子系列" refid="ydj_series"  lix="30" sbm="true" />
		<input group="基本信息" el="109" id="funitid" ek="fbillhead" fn="funitid" ts="" visible="-1" cn="基本单位" refid="ydj_unit" filter="fisbaseunit='1'" lix="35" must="1" />
		<input group="基本信息" el="109" id="fsalunitid" ek="fbillhead" fn="fsalunitid" ts="" visible="-1" cn="销售单位" refid="ydj_unit" lix="40" must="1" lock="-1" />
		<input group="基本信息" el="109" id="fpurunitid" ek="fbillhead" fn="fpurunitid" ts="" visible="-1" cn="采购单位" refid="ydj_unit" lix="41" must="1" lock="-1" />
		<input group="基本信息" el="109" id="fstockunitid" ek="fbillhead" fn="fstockunitid" ts="" visible="1150" cn="库存单位" refid="ydj_unit" lix="45" must="1" lock="-1" />
		<input group="基本信息" el="106" ek="FBillHead" id="fcostcategoryid" fn="fcostcategoryid" pn="fcostcategoryid" visible="1150" cn="存货类别" refid="ydj_productcategory" defval="'CHLB_SYS_01'" sbm="true" lix="50" />
		<input group="基本信息" el="102" ek="fbillhead" id="fsuttle" fn="fsuttle" pn="fsuttle" cn="净重(kg)" visible="1150" lix="55" format="0,000.000" sbm="true" />
		<input group="基本信息" el="102" ek="fbillhead" id="fvolume" fn="fvolume" pn="fvolume" cn="体积(m³)" visible="1150" lix="60" format="0,000.000" sbm="true" />
		<input group="基本信息" el="106" ek="fbillhead" id="fselcategoryid" fn="fselcategoryid" pn="fselcategoryid" refid="sel_category" ts="" visible="-1" cn="选配类别" lix="65" />
		<input group="基本信息" el="116" ek="fbillhead" id="funstdtype" fn="funstdtype" pn="funstdtype" type="checkbox" cn="非标产品" desc="非标产品" defval="false" visible="1150" lix="70" xlsin="0" lock="-1" copy="0" />
		<input group="基本信息" el="116" ek="fbillhead" id="finnovateflag" fn="finnovateflag" pn="finnovateflag" type="checkbox" cn="创新渠道产品标记" desc="创新渠道产品标记" defval="false" visible="1150" lix="75" xlsin="0" lock="-1" copy="0" />
		<input group="基本信息" el="116" ek="fbillhead" id="fbedpartflag" fn="fbedpartflag" pn="fbedpartflag" type="checkbox" cn="可添加配件" desc="可添加配件" defval="false" visible="1150" lix="80" xlsin="0" copy="0" />
		<input group="基本信息" el="116" ek="fbillhead" id="freplaceflag" fn="freplaceflag" pn="freplaceflag" type="checkbox" cn="补件标记" desc="补件标记" defval="false" visible="1150" lix="85" xlsin="0" lock="-1" copy="0" />
		<input group="基本信息" el="116" ek="fbillhead" id="fspecialflag" fn="fspecialflag" pn="fspecialflag" type="checkbox" cn="专供标记" defval="false" visible="1150" notrace="false" lix="86" xlsin="0" lock="-1" copy="0" />
		<input group="基本信息" el="116" ek="fbillhead" id="fispartflag" fn="fispartflag" pn="fispartflag" type="checkbox" cn="配件标记" defval="false" visible="1150" lix="86" xlsin="0" copy="0" />
		<input group="基本信息" el="116" ek="fbillhead" id="fsuiteflag" fn="fsuiteflag" pn="fsuiteflag" type="checkbox" cn="选配套件" desc="选配套件" defval="false" visible="1150" lix="90" xlsin="0" lock="-1" copy="0" />
		<input type="checkbox" id="fispresetprop" el="116" ek="fbillhead" fn="fispresetprop" pn="fispresetprop" cn="允许选配" defval="false" visible="1150" lix="95" xlsin="0" lock="-1" copy="0" />
		<input type="checkbox" id="fcustom" el="116" ek="fbillhead" fn="fcustom" pn="fcustom" cn="允许定制" desc="允许定制" defval="false" visible="1150" lix="97" xlsin="1" copy="0" />
		<input type="checkbox" id="fisvirtual" el="116" ek="fbillhead" fn="fisvirtual" pn="fisvirtual" cn="虚拟产品" desc="虚拟产品" defval="false" visible="-1" lix="97" xlsin="1" />
		<input type="checkbox" id="fendpurchase" el="116" ek="fbillhead" fn="fendpurchase" pn="fendpurchase" cn="停购" desc="停购" defval="false" visible="1150" lix="99" xlsin="1" sbm="false" copy="0" />
		<input group="基本信息" el="100" ek="fbillhead" id="foriginplace" fn="foriginplace" pn="foriginplace" cn="产地" visible="1150" lix="105" sbm="true" />
		<input group="基本信息" el="152" ek="fbillhead" id="fstate" fn="fstate" pn="fstate" visible="-1" cn="状态" lix="110" lock="-1"
			   vals="'0':'','1':'已发布','2':'待审核','3':'审核不通过','4':'已下架','5':'已停售','6':'已停购','7':'已停产'" defVal="'0'" />
		<input el="106" ek="fbillhead" id="fbizorgid" fn="fbizorgid" pn="fbizorgid" cn="组织" refid="bas_organization" defVal="@currentOrgId"
			   lock="-1" visible="0" copy="0" len="50" lix="115" desc="使用组织信息，用于组织隔离和控制商品的可编辑" />
		<input group="基本信息" el="138" id="fcontent" ek="fbillhead" fn="fcontent" ts="" cn="商品详情描述" visible="0" />
		<input group="基本信息" el="101" ek="fbillhead" id="fpackqty" fn="fpackqty" pn="fpackqty" cn="采购件数" visible="1150" lix="60" />
		<input group="基本信息" el="100" ek="fbillhead" id="fspecifica" fn="fspecifica" pn="fspecifica" cn="规格型号" visible="1150" lix="4" />
		<input group="基本信息" el="100" id="funstdcategory" ek="fbillhead" fn="funstdcategory" ts="" visible="-1" sbm="true" cn="非标类别" lix="10" />

		<input type="checkbox" id="fischeckheight" el="116" ek="fbillhead" fn="fischeckheight" pn="fischeckheight" cn="校验高度" defval="false" visible="1150" lix="95" xlsin="0" lock="-1" copy="0" />
		<input type="checkbox" id="fismusiproduct" el="116" ek="fbillhead" fn="fismusiproduct" pn="fismusiproduct" cn="慕思商品" defval="false" visible="1150" lix="95" xlsin="0" lock="-1"  copy="0" />

		<input group="包装信息" ek="fbillhead" el="116" type="checkbox" id="fisbarcode" fn="fisbarcode" pn="fisbarcode" cn="启用条码" defval="false" visible="1150" />
		<input group="包装信息" ek="fbillhead" el="152" id="fpackagtype" fn="fpackagtype" pn="fpackagtype" visible="-1" cn="打包类型" lock="0"
			   vals="'1':'标准','2':'1件多包','3':'1包多件'" defVal="'0'" />
		<input group="包装信息" ek="fbillhead" el="101" id="fpiece" fn="fpiece" pn="fpiece" cn="包装规则 (件)" visible="1150" lock="0" sbm="true" />
		<input group="包装信息" ek="fbillhead" el="101" id="fbag" fn="fbag" pn="fbag" cn="包装规则 (包)" visible="1150" lock="0" sbm="true" />

		<!--***************************** 隐藏字段开始 ***************************************************************************************************************************************************************************************-->
		<input group="基本信息" el="100" ek="fbillhead" visible="0" lix="40" id="fproductionaddress" fn="fproductionaddress" pn="fproductionaddress" cn="产地" />
		<input group="基本信息" el="102" ek="fbillhead" id="fgrossload" fn="fgrossload" pn="fgrossload" cn="毛重(kg)" visible="0" lix="7" format="0,000.00" sbm="false" />

		<input group="基本信息" el="100" ek="fbillhead" id="fattribute" fn="fattribute" pn="fattribute" cn="属性" visible="0" lix="4" width="100" len="2000" sbm="false" />
		<input group="基本信息" el="100" ek="fbillhead" id="fsize" fn="fsize" pn="fsize" cn="尺寸" visible="0" lix="5" sbm="false" />
		<input group="基本信息" el="100" ek="FBillHead" id="fpacksize" fn="fpacksize" pn="fpacksize" visible="0" cn="包装尺寸"
			   lock="0" copy="1" lix="5" notrace="true" ts="" sbm="false" />
		<input group="基本信息" el="100" ek="fbillhead" id="fsuppliernumber" fn="fsuppliernumber" pn="fsuppliernumber" cn="供应商货号" visible="0" lix="11" />
		<input group="基本信息" el="106" id="fsupplierid" ek="fbillhead" fn="fsupplierid" refid="ydj_supplier" ts="" ACL="1" visible="0" cn="所属供应商" lix="12" sbm="false" />
		<!--套件相关-->
		<input group="基本信息" el="116" ek="fbillhead" id="fissuit" fn="fissuit" pn="fissuit" cn="是否套件" visible="0" />
		<input group="基本信息" el="116" ek="fbillhead" id="fisindividualsale" fn="fisindividualsale" pn="fisindividualsale" cn="可单独销售" defval="true" visible="0" />
		<input group="基本信息" el="106" ek="fbillhead" id="fselectioncategoryid" fn="fselectioncategoryid" refid="mt_selectioncategory" ts="" visible="0" cn="选配类别" />
		<input group="基本信息" el="106" ek="fbillhead" id="fsuiteselectionid" fn="fsuiteselectionid" refid="mt_suiteselection" ts="" visible="0" cn="标准套件方案" />
		<!--款式信息-->
		<input group="款式信息" el="122" id="fspace" ek="fbillhead" fn="fspace" refId="bd_enum" dfld="fenumitem" ts="" cg="空间" visible="0" cn="空间" lix="14" sbm="false" />
		<input group="款式信息" el="122" id="fstyle" ek="fbillhead" fn="fstyle" refId="bd_enum" dfld="fenumitem" ts="" cg="风格" visible="0" cn="风格" lix="16" sbm="false" />
		<input group="款式信息" el="116" ek="fbillhead" visible="0" id="fiscontent" fn="fiscontent" pn="fiscontent" cn="是否有商品描述" lix="17" xlsin="0" />
		<input el="100" ek="fbillhead" id="fsynid" fn="fsynid" pn="fsynid" cn="协同商品id" lock="-1" visible="0" copy="0" />
		<input id="foldnumber" el="100" ek="fbillhead" fn="foldnumber" pn="foldnumber" cn="旧系统编码" desc="旧系统编码" copy="1" visible="1150" lix="1" width="110" />
		<input el="104" ek="fbillhead" id="fsalprice" fn="fsalprice" cn="销售价" width="100" visible="0" format="0,000.00" lix="90" />
		<input el="104" ek="fbillhead" id="fpurprice" fn="fpurprice" cn="采购价" width="100" visible="0" format="0,000.00" lix="100" />
		<input el="104" ek="fbillhead" id="fguideprice" fn="fguideprice" cn="品牌指导价" width="100" visible="0" format="0,000.00" lix="100" />
		<input el="132" ek="fbillhead" id="fdefattrinfo" fn="fdefattrinfo" cn="默认辅助属性" ctlfk="" desc="不需要指定ctlfk属性，默认指向自己" visible="0" lock="-1" lix="4" width="200" />
		<input type="checkbox" id="fallowshopsale" el="116" ek="fbillhead" fn="fallowshopsale" pn="fallowshopsale" cn="商城允销" defval="false" copy="0" visible="0" lix="20" xlsin="0" />
		<input type="checkbox" id="fenableserialno" el="116" ek="fbillhead" fn="fenableserialno" pn="fenableserialno" cn="启用序列号" desc="启用序列号" defval="false" copy="0" visible="0" lix="20" xlsin="0" />
		<input type="checkbox" id="fispulloff" el="116" ek="fbillhead" fn="fispulloff" pn="fispulloff" cn="停售" desc="停售" defval="false" visible="0" lix="25" xlsin="0" sbm="false" />
		<input type="checkbox" id="fstopproduction" el="116" ek="fbillhead" fn="fstopproduction" pn="fstopproduction" cn="停产" desc="停产" defval="false" visible="0" lix="30" xlsin="0" sbm="false" />
		<input type="checkbox" id="fpermitsel" el="116" ek="fbillhead" fn="fpermitsel" pn="fpermitsel" cn="允许选配(测试 无用)" desc="允许选配" defval="false" visible="0" lix="30" xlsin="0" />
		<!--是否固定辅助属性组合值-->
		<input type="checkbox" id="fisfixprop" el="116" ek="fbillhead" fn="fisfixprop" pn="fisfixprop" cn="固定组合" defval="false" visible="0" lix="30" xlsin="0" lock="-1" />
		<input lix="100" el="152" ek="fbillhead" id="fdeliverymode" fn="fdeliverymode" pn="fdeliverymode" visible="0" cn="提货方式" canchange="true"
			   lock="0" copy="0" notrace="true" ts="" vals="'0':'物流配送','1':'立即提货','2':'预约提货'" defVal="'0'" />
		<input el="112" ek="fbillhead" visible="0" lix="35" id="fstopdate" fn="fstopdate" pn="fstopdate" cn="停产日期" />
		<input el="100" ek="fbillhead" visible="0" lix="45" id="fproductposition" fn="fproductposition" pn="fproductposition" cn="产品定位" />
		<input el="100" ek="fbillhead" visible="0" lix="50" id="fproductbookno" fn="fproductbookno" pn="fproductbookno" cn="产品说明书编号" len="500" />
		<input el="100" ek="fbillhead" visible="0" lix="55" id="fproductinstallbookno" fn="fproductinstallbookno" pn="fproductinstallbookno" cn="产品安装说明书编号" len="500" />
		<input el="113" ek="fbillhead" visible="0" lix="60" id="fstartsalestime" fn="fstartsalestime" pn="fstartsalestime" cn="开始销售时间" />
		<input el="113" ek="fbillhead" visible="0" lix="65" id="fendsalestime" fn="fendsalestime" pn="fendsalestime" cn="结束销售时间" />
		<!--博领K3协同特殊中转字段，用于处理主物料（麦浩商品）与子物料（麦浩规格辅助属性值）的上下级关系，同一个物料的以下两个字段只会有一个字段有值-->
		<input el="100" ek="fbillhead" id="fk3childmaterialname" fn="" pn="fk3childmaterialname" cn="K3子物料名称" lock="-1" visible="0" copy="0"
			   desc="如果该物料是主物料且存在下属子物料的情况下，该字段才会有值，该字段记录的是当前主物料所关联所有子物料的名称，用于生成麦浩主商品的规格辅助属性值" />
		<input el="100" ek="fbillhead" id="fk3mainchaindataid" fn="" pn="fk3mainchaindataid" cn="K3主物料云链ID" lock="-1" visible="0" copy="0"
			   desc="如果该物料是子物料且关联有主物料的情况下，该字段才会有值，该字段记录的是当前子物料所关联主物料的云链ID，用于生成麦浩主商品的规格辅助属性值" />
		<input el="100" ek="fbillhead" id="fk3mainmaterial" fn="" pn="fk3mainmaterial" cn="是否是K3主物料" lock="-1" visible="0" copy="0" />
		<input el="100" ek="fbillhead" id="fk3textureauxprop" fn="" pn="fk3textureauxprop" cn="是否启用材质辅助属性" lock="-1" visible="0" copy="0" />
		<input el="100" ek="fbillhead" id="fk3colorauxprop" fn="" pn="fk3colorauxprop" cn="是否启用颜色辅助属性" lock="-1" visible="0" copy="0" />
		<input el="100" ek="fbillhead" id="fk3pulloff" fn="" pn="fk3pulloff" cn="是否是下架" lock="-1" visible="0" copy="0" />
		<input el="100" ek="fbillhead" id="fk3childsiblings" fn="" pn="fk3childsiblings" cn="子物料的所有同级子物料名称" lock="-1" visible="0" copy="0" len="2147483647"
			   desc="用于K3下架子物料时禁用麦浩商品规格辅助属性值映射中的明细行" />

		<input type="checkbox" id="fdelisting" el="116" ek="fbillhead" fn="fdelisting" pn="fdelisting" cn="退市" defval="false" copy="0" visible="0" lix="20" xlsin="0" />
		<!--***************************** 隐藏字段结束 ***************************************************************************************************************************************************************************************-->

	</div>

	<!--属性明细，该明细不存储，只是用于前端动态显示-->
	<table id="fpropentry" el="52" pk="fentryid" tn="" pn="fpropentry" cn="属性明细">
		<tr>
			<th el="106" ek="fpropentry" id="fpropid" fn="" pn="fpropid" cn="属性名" refid="sel_prop" sformid="" width="200" visible="96" lock="-1"></th>
			<th el="116" ek="fpropentry" id="fiswhetherinforce" fn="" pn="fiswhetherinforce" cn="是否生效" width="100" visible="96" lock="-1"></th>
			<th el="116" ek="fpropentry" id="fiscontrolmust" fn="" pn="fiscontrolmust" cn="是否必录" width="100" visible="96" lock="-1"></th>
			<th el="101" ek="fpropentry" id="fdisplayseq" fn="" pn="fdisplayseq" cn="显示顺序" width="100" visible="96" lock="-1"></th>
		</tr>
	</table>

	<!--产品销售组织-->
	<table id="fsaleorgentry" el="52" pk="fentryid" tn="t_bd_materialsaleorg" pn="fsaleorgentry" cn="产品销售组织" kfks="fsaleorgid">
		<tr>
			<th el="106" ek="fsaleorgentry" id="fsaleorgid" fn="fsaleorgid" pn="fsaleorgid" cn="销售组织" refid="bas_organization" sformid="" width="300" visible="1150" copy="0"></th>
			<th el="152" ek="fsaleorgentry" id="fdisablestatus" fn="fdisablestatus" pn="fdisablestatus" cn="禁用状态" vals="'1':'已启用','2':'已禁用'" defVal="'1'" width="100" visible="1150" lock="-1" align="center"  copy="0"></th>
			<th el="113" ek="fsaleorgentry" id="fdisabledate" fn="fdisabledate" pn="fdisabledate" cn="产品禁用时间" width="130" visible="1150" lock="-1"  copy="0"></th>
			<th el="100" ek="fsaleorgentry" id="fverifypricestatus" fn="fverifypricestatus" pn="fverifypricestatus" cn="核价状态" width="120" visible="1150" lock="-1"  copy="0"></th>
		</tr>
	</table>

	<!--包装明细-->
	<table id="fpackage" el="52" pk="fentryid" tn="t_bd_materialpackage" pn="fpackage" cn="包装明细">
		<tr>
			<th el="100" ek="fpackage" id="fpacknumber" fn="fpacknumber" pn="fpacknumber" cn="包件序号" width="100" visible="96" lock="-1"></th>
			<th el="100" ek="fpackage" id="fdescribe" fn="fdescribe" pn="fdescribe" cn="包件描述" width="100" visible="96"></th>
			<th el="100" ek="fpackage" id="fstandard" fn="fstandard" pn="fstandard" cn="包件规格" width="100" visible="96"></th>
			<th el="100" ek="fpackage" id="flength" fn="flength" pn="flength" cn="包件长" width="100" visible="96"></th>
			<th el="100" ek="fpackage" id="fwidth" fn="fwidth" pn="fwidth" cn="包件宽" width="100" visible="96"></th>
			<th el="100" ek="fpackage" id="fheight" fn="fheight" pn="fheight" cn="包件高" width="100" visible="96"></th>
			<th el="100" ek="fpackage" id="fpackvolume" fn="fpackvolume" pn="fpackvolume" cn="包件体积" width="100" visible="96"></th>
		</tr>
	</table>

	<!--单位明细-->
	<table id="funitentry" el="52" pk="fentryid" tn="t_bd_materialunit" pn="funitentry" cn="单位明细" kfks="funitid_e">
		<tr>
			<th el="109" ek="funitentry" id="funitid_e" fn="funitid" pn="funitid" cn="单位" refid="ydj_unit" sformid="" width="180" visible="1150" lock="-1"></th>
			<th el="152" ek="funitentry" id="fcvttype_e" fn="fcvttype" pn="fcvttype" cn="换算类型" vals="'1':'固定'" defVal="'1'" width="120" visible="1150"></th>
			<th el="102" ek="funitentry" id="fcvtrate_e" fn="fcvtrate" pn="fcvtrate" cn="换算率" format="0,000.0000000000" width="120" visible="1150"></th>
			<th el="101" ek="funitentry" id="fprecision_e" fn="fprecision" pn="fprecision" cn="精度" width="100" visible="1150" lock="-1"></th>
			<th el="100" ek="funitentry" id="fcvtdesc" fn="" pn="fcvtdesc" cn="换算说明：1单位=(换算率)基本单位" width="260" visible="96" lock="-1" desc="该字段不需要存到数据库"></th>
		</tr>
	</table>

	<!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
	<div id="opList">
		<ul el="10" ek="fbillhead" id="save" op="save" opn="保存" data="" permid="">
			<li el="11" id="save_valid_fnumber" cn="保存时编码唯一" vid="500" data="fmainorgid,fpublishcid,fnumber" precon=""></li>
			<li el="11" id="save_valid_fname" cn="保存时名称唯一" vid="500" remove></li>
			<!--<li el="11" vid="510" cn="品牌不能为空" data="{'expr':'fbrandid!=\'\' and fbrandid!=\' \'','message':'品牌不能为空！'}"></li>-->
			<li el="11" vid="510" cn="商品类别不能为空" data="{'expr':'fcategoryid!=\'\' and fcategoryid!=\' \'','message':'商品类别不能为空！'}"></li>
			<!--<li el="11" vid="510" cn="系列不能为空" data="{'expr':'fseriesid!=\'\' and fseriesid!=\' \'','message':'系列不能为空！'}"></li>-->
			<!--<li el="11" vid="510" cn="数据来源不能为空" data="{'expr':'fdataorigin!=\'\' and fdataorigin!=\' \'','message':'数据来源不能为空！'}"></li>-->
			<li el="11" vid="510" cn="基本单位不能为空" data="{'expr':'funitid!=\'\' and funitid!=\' \'','message':'基本单位不能为空！'}"></li>
			<li el="11" vid="510" cn="库存单位不能为空" data="{'expr':'fstockunitid!=\'\' and fstockunitid!=\' \'','message':'库存单位不能为空！'}"></li>
			<li el="11" vid="510" cn="销售单位不能为空" data="{'expr':'fsalunitid!=\'\' and fsalunitid!=\' \'','message':'销售单位不能为空！'}"></li>
			<li el="11" vid="510" cn="采购单位不能为空" data="{'expr':'fpurunitid!=\'\' and fpurunitid!=\' \'','message':'采购单位不能为空！'}"></li>
			<!--<li el="11" vid="510" cn="换算率必须大于0" data="{'expr':'funitid_e=\'\' or funitid_e=\' \' or (funitid_e!=\'\' and funitid_e!=\' \' and fcvtrate_e>0)','message':'单位明细中存在单位时，换算率必须大于0！'}"></li>-->
		</ul>
		<ul el="10" ek="fbillhead" id="updateprice" op="updateprice" opn="更新商品默认价" data="" permid="ydj_product_updateprice"></ul>
		<ul el="10" ek="fbillhead" id="MSSaveSync" op="MSSaveSync" opn="慕思协同保存" data="{'syncFieldIds': ['fnumber','fname','fseltypeid','fspecifica','fcategoryid','fbrandid','fseriesid','fsubseriesid','fauxseriesid','fdescription','fselcategoryid','funstdtype','finnovateflag','fbedpartflag','freplaceflag','fispresetprop','fcustom','fgrossload','fvolume','fsuiteflag','funitid','fsalunitid','fstockunitid','fpurunitid','fpackqty','fsaleorgid','fdisablestatus','fdisabledate','fverifypricestatus','fischeckheight','funstdcategory','fspecialflag','fispartflag']}" permid=""></ul>
	</div>

	<!--表单所涉及的权限项定义-->
	<div id="permList">
		<ul el="12" id="ydj_product_updateprice" cn="更新商品默认价"></ul>
	</div>

</body>
</html>