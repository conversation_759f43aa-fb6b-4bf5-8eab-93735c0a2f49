<!DOCTYPE html>
<html>
<head>
    <title></title>
	<meta charset="utf-8" />
</head>
<body id="ydj_brand" basemodel="bd_basetmpl" el="3" cn="品牌" isolate="0" IsAsynLstDesc="true" querydbnode="">
    <div id="fbillhead" el="51" pk="fid" tn="t_Ydj_Brand" pn="fbillhead" cn="品牌">
        <input el="102" ek="fbillhead" id="fcostratio" fn="fcostratio" cn="成本系数" width="110" format="0,000.00" visible="1150" lix="100" />
        <input el="152" ek="fbillhead" id="fmusibrand" fn="fmusibrand" pn="fmusibrand" visible="1150" cn="慕思品牌" lock="0" must="1"
               vals="'0':'否','1':'是'" />

        <input group="基本信息" el="116" ek="fbillhead" visible="-1" id="fauto_m1" fn="fauto_M1" pn="fauto_M1" cn="自动授权助眠" lix="30" width="90" />
        <input group="基本信息" el="116" ek="fbillhead" visible="-1" id="fauto_z1" fn="fauto_Z1" pn="fauto_Z1" cn="自动授权通配" lix="30" width="90" /> 
    </div>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="syncfrommusi" op="syncfrommusi" opn="从慕思拉取数据" permid="fw_syncfrommusi"></ul>
    </div>

    <div id="permList">
        <ul el="12" id="fw_syncfrommusi" cn="从慕思拉取数据"></ul>
    </div>
    
</body>
</html>

