<!DOCTYPE html>
<html>
<head>
    <title></title>
    <meta charset="utf-8" />
</head>
<body id="ydj_productdelisting" basemodel="" el="1" cn="工厂下市产品可生产量" isolate="1" rac="true" vom="3" IsAsynLstDesc="true" querydbnode="">
    <div id="fbillhead" el="51" pk="fid" tn="t_ydj_productdelisting" pn="fbillhead" cn="工厂下市产品可生产量">
        <input lix="1" group="基本信息" el="107" ek="fbillhead" id="fmtrlnumber" fn="fmtrlnumber" pn="fmtrlnumber" cn="商品编码"
               visible="-1" lock="-1" copy="1" ts="" ctlfk="fmaterialid" dispfk="fnumber" refvt="0" defls="true" />
        <input lix="2" group="基本信息" el="106" ek="fbillhead" id="fmaterialid" fn="fmaterialid" pn="fmaterialid" cn="商品"
               visible="-1" lock="-1" copy="0" ts="" refid="ydj_product" filter="" reflvt="0" dfld="fname,fspecifica,fvolume,fgrossload,fpacksize,fsize" defls="true" notrace="false" />
        <input lix="3" group="基本信息" el="106" ek="fbillhead" id="fseltypeid" fn="fseltypeid" pn="fseltypeid" cn="型号"
               visible="-1" lock="-1" copy="0" ts="" refid="sel_type" defls="true" notrace="false" />
        <input lix="4" group="基本信息" el="118" ek="fbillhead" id="fcreatorid" fn="fcreatorid" pn="fcreatorid" cn="创建人"
               visible="-1" lock="-1" copy="0" refId="sec_user" dfld="FName" xlsin="0" defls="true" type="text" />
        <input lix="5" group="基本信息" el="119" ek="fbillhead" id="fcreatedate" fn="fcreatedate" pn="fcreatedate" cn="创建日期"
               visible="-1" lock="-1" copy="0" xlsin="0" defls="true" type="datetime" width="130" />
        <input lix="6" group="基本信息" el="100" ek="fbillhead" visible="1150" id="fspecmaterial" fn="fspecmaterial" pn="fspecmaterial" cn="专用材料" copy="0" len="100" lock="-1" notrace="false" />
        <input lix="7" group="基本信息" el="100" ek="fbillhead" id="fhqid" fn="fhqid" pn="fhqid" cn="中台的数据ID（后台字段）"
               visible="61" lock="-1" copy="0" ts="" />
        <input lix="10" group="基本信息" el="116" ek="fbillhead" id="fpropvalue" pn="fpropvalue" fn="fpropvalue" cn="属性值编码" visible="0" lock="-1" copy="0" />
        <!-- 0标准 1定制 -->
        <input lix="10" group="基本信息" el="116" ek="fbillhead" id="ftype" fn="ftype" pn="ftype" cn="是否定制品" visible="1086" lock="-1" copy="0" />
        <input group="基本信息" el="106" id="fbrandid" ek="fbillhead" fn="fbrandid" ts="" visible="-1" cn="品牌" refid="ydj_brand" lix="20" sbm="true" lock="-1" copy="0" />
        <input group="基本信息" el="106" id="fseriesid" ek="fbillhead" fn="fseriesid" ts="" visible="-1" cn="系列" refid="ydj_series" lix="25" sbm="true" lock="-1" copy="0" />
    </div>

    <table id="fpropentry" el="52" pk="fentryid" tn="t_ydj_productdelistingpropentry" pn="fpropentry" cn="属性明细">
        <tr>
            <th lix="21" el="107" ek="fpropentry" id="fpropnumber" fn="fpropnumber" pn="fpropnumber" cn="属性编码（用于后期扩展，先不管）" ctlfk="fpropid" dispfk="fnumber" width="200" visible="0" lock="-1"></th>
            <th lix="22" el="106" ek="fpropentry" id="fpropid" fn="fpropid" pn="fpropid" cn="属性名称（用于后期扩展，先不管）" refid="sel_prop" sformid="" width="200" visible="0" lock="-1"></th>
            <th lix="23" el="107" ek="fpropentry" id="fpropvalnumber" fn="fpropvalnumber" pn="fpropvalnumber" cn="属性值编码" ctlfk="fpropvalueid" dispfk="fnumber" width="200" visible="1148" lock="-1"></th>
            <th lix="24" el="106" ek="fpropentry" id="fpropvalueid" fn="fpropvalueid" pn="fpropvalueid" cn="属性值名称" refid="sel_propvalue" sformid="" width="200" visible="1148" lock="-1"></th>
        </tr>
    </table>

    <table id="fentity" el="52" pk="fentryid" tn="t_ydj_productdelistingentry" pn="fentity" cn="数量明细">
        <tr>
            <th lix="11" el="103" ek="fentity" id="festimateqty" fn="festimateqty" pn="festimateqty" cn="预估可采购数" visible="-1" width="100" format="0,000.00" lock="-1"></th>
            <th lix="12" el="103" ek="fentity" id="fwarnqty" fn="fwarnqty" pn="fwarnqty" cn="预警数" visible="-1" width="100" format="0,000.00" lock="-1"></th>
            <th lix="13" el="113" ek="fentity" id="fhqpushtime" fn="fhqpushtime" pn="fhqpushtime" cn="总部下发时间" visible="-1" format="yyyy-MM-dd HH:mm:ss" lock="-1"></th>

            <th lix="14" el="103" ek="fentity" id="fsalcanplaceorderqty" fn="fsalcanplaceorderqty" pn="fsalcanplaceorderqty" cn="可销售数" visible="-1" width="100" format="0,000.00" lock="-1"></th>
            <th lix="15" el="103" ek="fentity" id="fsumsalorderqty" fn="fsumsalorderqty" pn="fsumsalorderqty" cn="累计销售已下单数量过渡计算值" visible="-1" width="100" format="0,000.00" lock="-1"></th>
            <th lix="15" el="103" ek="fentity" id="fsumsalorderqtyreal" fn="fsumsalorderqtyreal" pn="fsumsalorderqtyreal" cn="累计销售数" visible="-1" width="100" format="0,000.00" lock="-1"></th>
            <th lix="16" el="113" ek="fentity" id="fsalqtyupdatetime" fn="fsalqtyupdatetime" pn="fsalqtyupdatetime" cn="可销售数更新时间" visible="-1" format="yyyy-MM-dd HH:mm:ss" lock="-1"></th>

            <th lix="17" el="103" ek="fentity" id="fpurcanplaceorderqty" fn="fpurcanplaceorderqty" pn="fpurcanplaceorderqty" cn="可采购数" visible="-1" width="100" format="0,000.00" lock="-1"></th>
            <th lix="18" el="103" ek="fentity" id="fsumpurorderqty" fn="fsumpurorderqty" pn="fsumpurorderqty" cn="累计采购数" visible="-1" width="100" format="0,000.00" lock="-1"></th>
            <th lix="19" el="113" ek="fentity" id="fpurqtyupdatetime" fn="fpurqtyupdatetime" pn="fpurqtyupdatetime" cn="可采购数更新时间" visible="-1" format="yyyy-MM-dd HH:mm:ss" lock="-1"></th>
            <!-- 新增 -->
            <!--当标准品【累计采购已下单量】大于下市表标准品【预计可下单采购量】，大于的数量值，赋值在此。-->
            <th lix="18" el="103" ek="fentity" id="fmatoccupysumpurqty" fn="fmatoccupysumpurqty" pn="fmatoccupysumpurqty" cn="占用型号采购数" visible="-1" width="100" format="0,000.00" lock="-1"></th>
            <!--当标准品【累计销售已下单量】大于下市表标准品【预计可下单采购量】，大于的数量值，赋值在此。-->
            <th lix="18" el="103" ek="fentity" id="fmatoccupysumsalqty" fn="fmatoccupysumsalqty" pn="fmatoccupysumsalqty" cn="占用型号累计销售数" visible="0" width="100" format="0,000.00" lock="-1"></th>
            <!--等于匹配到的对应“型号”或者“型号+颜色”的【采购可下单数量】。-->
            <th lix="18" el="103" ek="fentity" id="fsumpurqty" fn="fsumpurqty" pn="fsumpurqty" cn="型号可采购数" visible="-1" width="100" format="0,000.00" lock="-1"></th>
            <!--与当前型号所有有关的标准品【占用型号采购数】之和-->
            <th lix="18" el="103" ek="fentity" id="finvoccupysumpurqty" fn="finvoccupysumpurqty" pn="finvoccupysumpurqty" cn="标准品占用型号采购数" visible="-1" width="100" format="0,000.00" lock="-1"></th>
            <!--与当前型号所有有关的标准品【占用型号累计销售已下单数量】之和。-->
            <th lix="18" el="103" ek="fentity" id="finvoccupysumsalqty" fn="finvoccupysumsalqty" pn="finvoccupysumsalqty" cn="库存占用累计销售已下单数量" visible="0" width="100" format="0,000.00" lock="-1"></th>
            <!-- 0无效，1有效 -->
            <th lix="20" el="116" ek="fentity" id="fenable" fn="fenable" pn="fenable" visible="1086" cn="是否有效行" copy="0" lock="-1"></th>
            <!--默认“未更新”。中台下发结果后，金蝶初始化计算成功时，更新为“更新成功”；计算失败时，更新为“更新失败”。
    备注：初始化计算涉及（累计销售已下单数量、累计采购已下单数量、占用定制累计采购已下单数量、占用定制累计销售已下单数量、采购可下单数量、销售可下单数量、可使用定制采购可下单数量）-->
            <th lix="30" el="152" ek="fentity" id="fmodifystatus" fn="fmodifystatus" pn="fmodifystatus" visible="1150" cn="初始更新情况" copy="0" notrace="false" ts="" vals="'1':'未更新','2':'更新成功','3':'更新失败'" lock="-1"></th>
        </tr>
    </table>

    <!--表单默认操作列表，注：id可以与op不同，当一个op实现逻辑相同，只是传入参数不同时，那op可以一样，但id不能相同，常用于表单修改状态操作。-->
    <div id="opList">
        <ul el="10" ek="fbillhead" id="query" op="query" opn="查看" data="" permid="fw_view"></ul>
        <ul el="10" ek="fbillhead" id="viewrpt" op="viewrpt" opn="查看" data="" permid="fw_view"></ul>
        <ul el="10" ek="fbillhead" id="retryupdate" op="retryupdate" opn="初始数据更新重试" data="" permid="fw_retryupdate"></ul>
        <ul el="10" ek="fbillhead" id="delete" op="delete" opn="删除" data="" permid="fw_delete"></ul>
    </div>

    <div id="permList">
        <ul el="12" id="fw_view" cn="查看"></ul>
        <ul el="12" id="fw_retryupdate" cn="初始数据更新重试"></ul>
        <ul el="12" id="fw_delete" cn="删除" order="5"></ul>
    </div>

</body>
</html>

