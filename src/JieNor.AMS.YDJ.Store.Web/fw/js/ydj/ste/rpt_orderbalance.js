(function () {
    var rpt_orderbalance = (function (_super) {
        var _child = function (args) {
            var that = this;
            _super.call(that, args);
        };
        __extends(_child, _super);


        //表单初始化事件
        _child.prototype.onInitialized = function (e) {
            var that = this;
        }

        _child.prototype.onFieldValueFormat = function (e) {
            if (!e.id) return;
            switch (e.id.toLowerCase()) {
                case 'outdate':
                case 'fdeliveryplandate':
                case 'reservedateto':
                case 'fclosedate':
                    if (e.value == '1900-01-01 00:00:00') {
                        e.value = '';
                        e.cancel = true;
                    }
                    break;
            }
        }

        return _child;
    })(BasePlugIn);
    window.rpt_orderbalance = window.rpt_orderbalance || rpt_orderbalance;
})();